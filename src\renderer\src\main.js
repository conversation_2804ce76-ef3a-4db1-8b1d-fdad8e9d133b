import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import ElementPlus from 'element-plus'
import { createPinia } from 'pinia'
import 'element-plus/dist/index.css'
import 'element-plus/theme-chalk/dark/css-vars.css'

import './assets/iconfont/iconfont.js'
import './assets/css/icon.css'
const pinia = createPinia()

createApp(App).use(router).use(ElementPlus).use(pinia).mount('#app')
