<template>
  <div class="join-group">
    <!-- 标题栏 -->
    <div class="header">
      <span class="title">申请加群</span>
      <button class="close-btn" @click="closeDialog">
        <svg class="icon" aria-hidden="true">
          <use xlink:href="#icon-fork"></use>
        </svg>
      </button>
    </div>

    <!-- 群聊信息 -->
    <div class="group-info">
      <img :src="groupInfo.avatar" :alt="groupInfo.groupName" class="group-avatar" />
      <div class="group-details">
        <div class="group-name">{{ groupInfo.groupName }}</div>
        <div class="group-members">
          <span class="member-icon">👥</span>
          {{ groupInfo.currentCount }}/{{ groupInfo.countLimit }}
        </div>
      </div>
    </div>

    <!-- 表单内容 -->
    <div class="form-content">
      <!-- 填写验证信息 -->
      <div class="form-group">
        <label class="form-label">填写验证信息</label>
        <textarea
          v-model="verifyMessage"
          class="form-textarea"
          placeholder="请输入验证信息..."
          rows="4"
        ></textarea>
      </div>

      <!-- 入群介绍 -->
      <div class="form-group">
        <label class="form-label">入群后在群聊中发送进群介绍（选填）</label>
        <textarea
          v-model="groupIntroduction"
          class="form-textarea"
          placeholder="请输入进群介绍..."
          rows="4"
        ></textarea>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="footer">
      <button class="cancel-btn" @click="closeDialog">取消</button>
      <button class="send-btn" @click="sendRequest">发送</button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { addGroup } from '../api/group'
import { ElMessage } from 'element-plus'

// 群聊信息（从主进程接收）
const groupInfo = ref({
  groupName: '',
  avatar: '',
  currentCount: 0,
  countLimit: 0,
  groupId: ''
})

// 监听来自主进程的群聊信息
const handleSetGroupInfo = (event, groupData) => {
  groupInfo.value = {
    groupName: groupData.groupName,
    avatar: groupData.avatar,
    currentCount: groupData.currentCount,
    countLimit: groupData.countLimit,
    groupId: groupData.groupId
  }
}

onMounted(() => {
  // 监听主进程发送的群聊信息
  window.electron.ipcRenderer.on('set-group-info', handleSetGroupInfo)

  // 主动请求群聊信息（备用方案）
  setTimeout(() => {
    window.electron.ipcRenderer.send('request-group-info')
  }, 50)
})

onUnmounted(() => {
  // 清理事件监听
  window.electron.ipcRenderer.removeListener('set-group-info', handleSetGroupInfo)
})

// 表单数据
const verifyMessage = ref('')
const groupIntroduction = ref('')

// 方法
const closeDialog = () => {
  // 通知主进程关闭申请加群窗口
  window.electron.ipcRenderer.send('close-join-group-window')
}

// 申请加群
const sendRequest = async () => {
  const requestData = {
    groupId: groupInfo.value.groupId,
    noticeType: 3,
    content: '加群',
    joinGroupStatus: 0,
    leaveWord: verifyMessage.value,
    joinGroupGreeting: groupIntroduction.value
  }

  try {
    const { data } = await addGroup(requestData)
    if (data) {
      ElMessage({
        showClose: true,
        message: '入群申请发送成功',
        type: 'success'
      })
      // 关闭窗口
      setTimeout(() => {
        closeDialog()
      }, 500)
    } else {
      ElMessage({
        showClose: true,
        message: '申请失败',
        type: 'error'
      })
    }
  } catch (error) {
    console.error('发送入群申请失败:', error)
    ElMessage({
      showClose: true,
      message: '发送失败，请重试',
      type: 'error'
    })
  }
}
</script>

<style lang="scss" scoped>
.join-group {
  width: 100%;
  height: 100vh;
  background: #f2f2f2;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .header {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 12px 16px;
    background: transparent;
    flex-shrink: 0;
    position: relative;
    -webkit-app-region: drag;
    /* 使标题栏可拖拽 */

    .title {
      font-size: 14px;
      font-weight: 500;
      color: #333;
      text-align: center;
    }

    .close-btn {
      background: none;
      border: none;
      font-size: 18px;
      color: #999;
      cursor: pointer;
      padding: 0;
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      position: absolute;
      right: 16px;
      -webkit-app-region: no-drag;
      /* 关闭按钮不可拖拽 */

      &:hover {
        color: #666;
      }
    }
  }

  .group-info {
    display: flex;
    align-items: center;
    padding: 16px;
    flex-shrink: 0;
    background: #f2f2f2;
    margin-left: 5px;

    .group-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      margin-right: 10px;
      object-fit: cover;
    }

    .group-details {
      .group-name {
        font-size: 14px;
        font-weight: 500;
        color: #333;
        margin-bottom: 2px;
      }

      .group-members {
        font-size: 12px;
        color: #999;
        display: flex;
        align-items: center;

        .member-icon {
          margin-right: 4px;
        }
      }
    }
  }

  .form-content {
    flex: 1;
    padding: 16px 24px;
    overflow-y: auto;
    background: #f2f2f2;

    /* 自定义滚动条样式 */
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }

    &::-webkit-scrollbar-thumb:active {
      background: #909090;
    }
  }

  .form-group {
    margin-bottom: 20px;

    .form-label {
      display: block;
      font-size: 13px;
      color: #a5a5a5;
      margin-bottom: 8px;
      margin-left: 8px;
    }

    .form-textarea {
      width: 100%;
      padding: 8px 10px;
      border: 1px solid #fff;
      /* 边框改为白色 */
      border-radius: 4px;
      font-size: 13px;
      color: #333;
      resize: none;
      /* 禁用调整大小功能，隐藏右下角图标 */
      min-height: 60px;
      font-family: inherit;
      box-sizing: border-box;

      &:focus {
        outline: none;
        border-color: #1890ff;
      }

      &::placeholder {
        color: #999;
      }
    }
  }

  .footer {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    padding: 12px 16px;
    border-top: 1px solid #f0f0f0;
    background: #f2f2f2;
    flex-shrink: 0;

    .cancel-btn {
      padding: 6px 16px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      background: #fff;
      color: #666;
      font-size: 13px;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        border-color: #bbb;
        color: #333;
      }
    }

    .send-btn {
      padding: 6px 16px;
      border: none;
      border-radius: 4px;
      background: #1890ff;
      color: #fff;
      font-size: 13px;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        background: #40a9ff;
      }
    }
  }
}
</style>
