// http.js
import axios from 'axios'
import Api from './url'
import { ElMessage } from 'element-plus';

const Axios = axios.create({
  baseURL: (import.meta.env.PROD ? Api.prodDomain : "") + "/api",
  timeout: 10000,
})

// 添加请求拦截器
/* Axios.interceptors.request.use(req => {
  let token = localStorage.getItem('token')
  token && (req.headers.Authorization = token)
  return req
}, err => {
  return Promise.reject(err)
}) */

// 响应拦截器
Axios.interceptors.response.use(res => {
  if (res.status === 200 && res.data.code === 0) {
    return res.data;
  } else {
    ElMessage({
      showClose: true,
      message: res.data.message,
      type: 'error'
    })
    return Promise.reject(res.data.message)
  }
}, err => {
  ElMessage({
    showClose: true,
    message: err.message,
    type: 'error'
  })
  return Promise.reject(err)
})

export default Axios