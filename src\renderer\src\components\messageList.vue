<template>
  <div id="messageList" ref="messageListRef">
    <div class="message-list">
      <!-- 空状态占位符，确保消息列表区域占用空间 -->
      <div v-if="!messageList || messageList.length === 0" class="empty-state"></div>
      <div v-for="message in messageList" :key="message.messageId" class="message-item">
        <!-- 时间显示 -->
        <div v-if="isShow(message.messageId, message.createTime)" class="time-divider">
          <span class="time-text">{{ msgDatetimeCope(message.createTime) }}</span>
        </div>

        <!-- 消息内容 -->
        <div
          v-if="Number(message.userId || message.senderId) !== Number(userInfo?.userId)"
          class="message-wrapper"
        >
          <img :src="message.avatar" class="message-avatar" />
          <div class="message-content">
            <div class="message-header">
              <span class="username">{{ message.nickname }}</span>
            </div>
            <div class="message-text">{{ message.content }}</div>
          </div>
        </div>

        <div v-else class="me-message">
          <div>
            <div>{{ message.content }}</div>
          </div>
          <img :src="message.avatar" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, nextTick, ref, watch, onMounted, onUnmounted } from 'vue'
import { useChatStore } from '../store/chatStore'
import { msgDatetimeCope, isShowTime } from '../utils/global'

const messageList = defineModel({ required: true })
const messageListRef = ref(null)

const chatStore = useChatStore()

// 使用 computed 确保 userInfo 是响应式的
const userInfo = computed(() => chatStore.getUserInfo())

// 滚动到底部的方法
const scrollToBottom = () => {
  nextTick(() => {
    if (messageListRef.value) {
      const messageListElement = messageListRef.value.querySelector('.message-list')
      if (messageListElement) {
        messageListElement.scrollTop = messageListElement.scrollHeight
      }
    }
  })
}

// 更新自定义滚动条位置
const updateCustomScrollbar = () => {
  nextTick(() => {
    if (messageListRef.value) {
      const container = messageListRef.value
      const messageListElement = container.querySelector('.message-list')

      if (messageListElement) {
        const { scrollTop, scrollHeight, clientHeight } = messageListElement

        // 计算滚动条滑块的高度和位置
        const scrollbarHeight = clientHeight - 16 // 减去上下padding
        const thumbHeight = Math.max(20, (clientHeight / scrollHeight) * scrollbarHeight)
        const thumbTop =
          8 + (scrollTop / (scrollHeight - clientHeight)) * (scrollbarHeight - thumbHeight)

        // 更新滚动条滑块样式
        const beforeElement = container
        if (beforeElement) {
          beforeElement.style.setProperty('--thumb-height', `${thumbHeight}px`)
          beforeElement.style.setProperty('--thumb-top', `${thumbTop}px`)
        }
      }
    }
  })
}

// 监听消息列表变化，自动滚动到底部
watch(
  () => messageList.value?.length,
  (newLength, oldLength) => {
    if (
      newLength > oldLength &&
      messageList.value &&
      messageList.value.length > 0 &&
      userInfo.value?.userId
    ) {
      const lastMessage = messageList.value[messageList.value.length - 1]
      // 只有当最后一条消息是当前用户发送的时候才滚动到底部
      if (
        lastMessage &&
        (lastMessage.userId === userInfo.value.userId ||
          lastMessage.senderId === userInfo.value.userId)
      ) {
        scrollToBottom()
      }
    }
  }
)

// 监听消息列表初始化，滚动到底部
watch(
  () => messageList.value,
  (newList) => {
    if (newList && newList.length > 0) {
      scrollToBottom()
      updateCustomScrollbar()
    }
  },
  { immediate: true }
)

// 组件挂载后添加滚动事件监听
let scrollListener = null

onMounted(() => {
  nextTick(() => {
    if (messageListRef.value) {
      const messageListElement = messageListRef.value.querySelector('.message-list')
      if (messageListElement) {
        scrollListener = () => updateCustomScrollbar()
        messageListElement.addEventListener('scroll', scrollListener)
        updateCustomScrollbar() // 初始化滚动条位置
      }
    }
  })
})

onUnmounted(() => {
  if (messageListRef.value && scrollListener) {
    const messageListElement = messageListRef.value.querySelector('.message-list')
    if (messageListElement) {
      messageListElement.removeEventListener('scroll', scrollListener)
    }
  }
})

// 是否显示消息的时间
const isShow = (messageId, currTime) => {
  try {
    // 安全检查
    if (!messageList.value || messageList.value.length === 0) {
      return true
    }
    const currentIndex = messageList.value.findIndex(
      (item) => Number(item.messageId) === Number(messageId)
    )
    // 如果是第一条消息或找不到消息，直接显示时间
    if (currentIndex <= 0) {
      return true
    }
    // 获取上一条消息
    const lastMessage = messageList.value[currentIndex - 1]
    if (!lastMessage || !lastMessage.createTime) {
      return true
    }
    return isShowTime(lastMessage.createTime, currTime)
  } catch (error) {
    console.warn('isShow function error:', error)
    return true // 出错时默认显示时间
  }
}
</script>

<style scoped>
#messageList {
  height: 100%;
  display: flex;
  flex-direction: column;

  .message-list {
    flex: 1;
    padding: 8px;
    overflow-y: auto;
    height: 0;
    /* 关键：强制 flex 子元素计算高度 */

    /* 隐藏默认滚动条 */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE */

    &::-webkit-scrollbar {
      display: none; /* Chrome, Safari, Edge */
    }

    .message-item {
      margin-bottom: 20px;

      .time-divider {
        text-align: center;
        margin: 16px 0 12px 0;

        .time-text {
          background-color: #f0f0f0;
          color: #999;
          font-size: 12px;
          padding: 4px 12px;
          border-radius: 12px;
          display: inline-block;
        }
      }

      .message-wrapper {
        display: flex;
        align-items: flex-start;
        padding: 0 16px;

        .message-avatar {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          margin-right: 12px;
          flex-shrink: 0;
          object-fit: cover;
        }

        .message-content {
          flex: 0 1 auto;
          min-width: 0;

          .message-header {
            margin-bottom: 6px;

            .username {
              font-size: 13px;
              color: #666;
              font-weight: 500;
            }
          }

          .message-text {
            background-color: #fff;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 14px;
            line-height: 1.5;
            word-wrap: break-word;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
            display: inline-block;
            max-width: 400px;
            min-width: 20px;

            &:hover {
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }
          }
        }
      }

      .me-message {
        display: flex;
        align-items: flex-start;
        padding: 0 16px;
        justify-content: flex-end;

        > div {
          flex: 0 1 auto;
          min-width: 0;
          margin-right: 12px;

          > div {
            background-color: #1890ff;
            color: #fff;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 14px;
            line-height: 1.5;
            word-wrap: break-word;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            display: inline-block;
            max-width: 400px;
            min-width: 20px;
            text-align: left;

            &:hover {
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
            }
          }
        }

        img {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          flex-shrink: 0;
          object-fit: cover;
        }
      }
    }
  }
}

#messageList {
  position: relative;

  /* 隐藏原生滚动条但保持滚动功能 */
  .message-list {
    /* 隐藏滚动条但保持滚动功能 */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE 10+ */

    &::-webkit-scrollbar {
      width: 0px;
      background: transparent;
    }
  }

  /* 自定义滚动条轨道 */
  &::after {
    content: '';
    position: absolute;
    right: 4px;
    top: 8px;
    bottom: 8px;
    width: 6px;
    border-radius: 3px;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    z-index: 10;
  }

  /* 自定义滚动条滑块 */
  &::before {
    content: '';
    position: absolute;
    right: 4px;
    top: var(--thumb-top, 8px);
    width: 6px;
    height: var(--thumb-height, 20px);
    background: rgba(0, 0, 0, 0.3);
    border-radius: 3px;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 11;
  }

  /* 悬浮时显示滚动条 */
  &:hover {
    &::after {
      opacity: 1;
    }

    &::before {
      opacity: 1;
    }
  }
}

.empty-state {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 14px;
}
</style>
