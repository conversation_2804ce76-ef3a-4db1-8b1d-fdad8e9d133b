<template>
  <div class="edit-profile">
    <!-- 可拖动的标题栏 -->
    <div class="drag-header" style="-webkit-app-region: drag;">
      <div class="title">编辑资料</div>
      <el-button type="text" @click="handleClose" class="close-btn" style="-webkit-app-region: no-drag;">
        <el-icon>
          <Close />
        </el-icon>
      </el-button>
    </div>

    <el-scrollbar class="content">
      <div class="avatar-section">
        <el-avatar :size="80" :src="userAvatar" class="avatar" @click="handleAvatarClick">
          <img src="../assets/icon.png" alt="默认头像" />
        </el-avatar>
        <!-- 隐藏的文件选择器 -->
        <input ref="fileInput" type="file" accept="image/*" style="display: none" @change="handleFileChange" />
      </div>

      <el-form class="form-section" label-position="left" label-width="60px">
        <el-form-item label="昵称">
          <el-input v-model="nickname" maxlength="36" show-word-limit placeholder="请输入昵称" />
        </el-form-item>

        <el-form-item label="个签">
          <el-input v-model="signature" maxlength="80" show-word-limit type="textarea" :rows="2"
            placeholder="请输入个性签名" />
        </el-form-item>

        <el-form-item label="性别">
          <el-select v-model="gender" placeholder="请选择性别" class="full-width">
            <el-option label="男" value="男" />
            <el-option label="女" value="女" />
            <el-option label="其他" value="其他" />
          </el-select>
        </el-form-item>

        <el-form-item label="生日">
          <el-date-picker v-model="birthday" type="date" placeholder="选择日期" format="YYYY-MM-DD"
            value-format="YYYY-MM-DD" class="full-width" />
        </el-form-item>

        <el-form-item label="国家">
          <el-select v-model="country" placeholder="请选择国家" class="full-width" disabled>
            <el-option label="中国" value="中国" />
          </el-select>
        </el-form-item>

        <el-form-item label="省份">
          <el-select v-model="province" placeholder="请选择省份" class="full-width" @change="handleProvinceChange">
            <el-option label="请选择" value="请选择" />
            <el-option v-for="provinceItem in provinces" :key="provinceItem.id" :label="provinceItem.name"
              :value="provinceItem.name" />
          </el-select>
        </el-form-item>

        <el-form-item label="地区">
          <el-select v-model="region" placeholder="请选择地区" class="full-width" :disabled="province === '请选择'">
            <el-option label="请选择" value="请选择" />
            <el-option v-for="regionItem in regions" :key="regionItem.id" :label="regionItem.name"
              :value="regionItem.name" />
          </el-select>
        </el-form-item>
      </el-form>
    </el-scrollbar>

    <div class="footer">
      <el-button @click="handleClose" class="cancel-btn">取消</el-button>
      <el-button type="primary" @click="handleSave" class="save-btn">保存</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue';
import { useChatStore } from '../../store/chatStore';
import { ElMessage } from 'element-plus';
import { Close } from '@element-plus/icons-vue';
import { updateUserInfo, updateAvatar, getProvinces, getRegions } from '../../api/user';
import { genderEnum, genderReverseEnum, fileTypeEnum } from '../../utils/global';

const chatStore = useChatStore();
const userInfo = computed(() => chatStore.getUserInfo());

// 用户头像
const userAvatar = ref('../assets/icon.png');

// 文件选择器引用
const fileInput = ref(null);

// 表单数据
const nickname = ref(userInfo.value?.nickname || '');
const signature = ref(userInfo.value?.signiture || '');
const gender = ref(userInfo.value?.gender || '男');
const birthday = ref(userInfo.value?.birthday || '');
const country = ref(userInfo.value?.country || '中国');
const province = ref(userInfo.value?.province || '请选择');
const region = ref(userInfo.value?.region || '请选择');

// 省份和地区数据
const provinces = ref([]);
const regions = ref([]);
const selectedProvinceCode = ref('');

// 加载省份数据
const loadProvinces = async () => {
  try {
    const { data } = await getProvinces();
    provinces.value = data;
  } catch (error) {
    ElMessage.error('加载省份数据失败');
  }
};

// 处理省份变化
const handleProvinceChange = async (provinceName, keepCurrentRegion = false) => {

  // 保存当前地区值
  const currentRegion = region.value;

  // 重置地区选择（除非明确要求保持）
  if (!keepCurrentRegion) {
    region.value = '请选择';
  }
  regions.value = [];
  selectedProvinceCode.value = '';

  if (provinceName === '请选择') {
    return;
  }

  // 查找选中省份的code
  const selectedProvince = provinces.value.find(p => p.name === provinceName);
  if (!selectedProvince) {
    return;
  }

  selectedProvinceCode.value = selectedProvince.code;

  // 加载对应的地区数据
  try {
    const { data } = await getRegions(selectedProvince.code);
    regions.value = data;

    // 如果要保持当前地区，且该地区在新数据中存在，则恢复
    if (keepCurrentRegion && currentRegion && currentRegion !== '请选择') {
      const regionExists = data.some(r => r.name === currentRegion);
      if (regionExists) {
        region.value = currentRegion;
      } else {
        region.value = '请选择';
      }
    }
  } catch (error) {
    ElMessage.error('加载地区数据失败');
  }
};

// 初始化用户数据
const initUserData = async () => {
  if (userInfo.value) {
    nickname.value = userInfo.value.nickname || '';
    signature.value = userInfo.value.signiture || '';
    gender.value = genderEnum[userInfo.value.gender] || '男';
    birthday.value = userInfo.value.birthday || '';
    country.value = userInfo.value.country || '中国';
    province.value = userInfo.value.province || '请选择';
    region.value = userInfo.value.region || '请选择';

    // 设置头像
    if (userInfo.value.avatar) {
      userAvatar.value = userInfo.value.avatar;
    }

    if (userInfo.value.province && userInfo.value.province !== '请选择') {
      // 先保存用户的原始地区信息
      const originalRegion = userInfo.value.region;
      await handleProvinceChange(userInfo.value.province, true);
      // 确保地区值被正确设置
      if (originalRegion && originalRegion !== '请选择') {
        region.value = originalRegion;
      }
    }
  }
};

// 创建可序列化的用户信息对象（排除 Proxy 对象）
const createSerializableUserInfo = (baseUserInfo, updatedUserInfo) => {
  try {
    // 安全地处理 vips 字段
    let vipsArray = [];
    if (baseUserInfo?.vips) {
      if (Array.isArray(baseUserInfo.vips)) {
        vipsArray = [...baseUserInfo.vips];
      } else {
        // 如果是 Proxy 对象，尝试转换
        vipsArray = JSON.parse(JSON.stringify(baseUserInfo.vips));
      }
    }

    return {
      userId: baseUserInfo?.userId || updatedUserInfo.userId,
      account: baseUserInfo?.account || updatedUserInfo.account,
      nickname: updatedUserInfo.nickname,
      gender: updatedUserInfo.gender,
      avatar: baseUserInfo?.avatar || updatedUserInfo.avatar,
      signiture: updatedUserInfo.signiture,
      birthday: updatedUserInfo.birthday,
      country: updatedUserInfo.country,
      province: updatedUserInfo.province,
      region: updatedUserInfo.region,
      likeCount: baseUserInfo?.likeCount || updatedUserInfo.likeCount,
      state: baseUserInfo?.state || updatedUserInfo.state,
      grade: baseUserInfo?.grade || updatedUserInfo.grade,
      // 使用安全处理后的 vips 数组
      vips: vipsArray
    };
  } catch (error) {
    // 如果出错，返回不包含 vips 的基本信息
    return {
      userId: baseUserInfo?.userId || updatedUserInfo.userId,
      account: baseUserInfo?.account || updatedUserInfo.account,
      nickname: updatedUserInfo.nickname,
      gender: updatedUserInfo.gender,
      avatar: baseUserInfo?.avatar || updatedUserInfo.avatar,
      signiture: updatedUserInfo.signiture,
      birthday: updatedUserInfo.birthday,
      country: updatedUserInfo.country,
      province: updatedUserInfo.province,
      region: updatedUserInfo.region,
      likeCount: baseUserInfo?.likeCount || updatedUserInfo.likeCount,
      state: baseUserInfo?.state || updatedUserInfo.state,
      grade: baseUserInfo?.grade || updatedUserInfo.grade,
      vips: []
    };
  }
};

// 从 localStorage 获取用户信息的函数
const loadUserInfoFromStorage = async () => {
  try {
    const storedUserInfo = localStorage.getItem('userInfo');
    if (storedUserInfo) {
      const userData = JSON.parse(storedUserInfo);

      // 设置到 chatStore
      chatStore.setUserInfo(userData);
      // 更新表单数据，使用性别映射
      nickname.value = userData.nickname || '';
      signature.value = userData.signiture || '';
      gender.value = genderEnum[userData.gender] || '男';
      birthday.value = userData.birthday || '';
      country.value = userData.country || '中国';

      // 先设置地区，再设置省份，避免触发 watch 重置地区
      region.value = userData.region || '请选择';
      province.value = userData.province || '请选择';

      // 如果有省份和地区信息，加载对应的地区数据以确保下拉列表有选项
      if (userData.province && userData.province !== '请选择') {
        await handleProvinceChange(userData.province, true); // 保持当前地区
      }

      // 设置头像
      if (userData.avatar) {
        userAvatar.value = userData.avatar;
      }
      return true;
    }
  } catch (error) {
    console.error('解析 localStorage 用户信息失败:', error);
  }
  return false;
};


// 监听省份变化，自动加载地区数据
watch(province, async (newProvince) => {
  if (newProvince && newProvince !== '请选择') {
    await handleProvinceChange(newProvince, false);
  }
});

onMounted(async () => {
  try {

    // 加载省份数据
    await loadProvinces();

    // 首先尝试从 chatStore 获取数据
    await initUserData();

    // 如果 chatStore 没有数据，尝试从 localStorage 获取
    if (!userInfo.value) {
      await loadUserInfoFromStorage();
    }
  } catch (error) {
    console.error('初始化过程中出错:', error);
  }
});

// 保存用户信息
const handleSave = async () => {
  if (!nickname.value.trim()) {
    ElMessage.warning('昵称不能为空');
    return;
  }

  const { data } = await updateUserInfo({
    nickname: nickname.value,
    signiture: signature.value,
    gender: genderReverseEnum[gender.value] || 1, // 转换为后端需要的数字格式
    birthday: birthday.value,
    country: country.value,
    province: province.value,
    region: region.value
  })
  if (!data) {
    ElMessage.error('保存失败');
    return;
  }

  const updatedUserInfo = {
    ...userInfo.value,
    nickname: nickname.value,
    signiture: signature.value,
    gender: genderReverseEnum[gender.value] || 1, // 保存数字格式到store
    birthday: birthday.value,
    country: country.value,
    province: province.value,
    region: region.value
  };

  // 更新本地状态
  chatStore.setUserInfo(updatedUserInfo);

  // 创建可序列化的用户信息对象
  const cleanUserInfo = createSerializableUserInfo(userInfo.value, updatedUserInfo);

  // 同时更新 localStorage
  localStorage.setItem('userInfo', JSON.stringify(cleanUserInfo));

  // 通知主进程更新其他窗口的用户信息
  if (window.electron && window.electron.ipcRenderer) {
    console.log('editProfile.vue: 发送用户信息更新到主进程:', cleanUserInfo);
    window.electron.ipcRenderer.send('user-info-updated', cleanUserInfo);
  }

  // 提示保存成功
  ElMessage.success('保存成功');

  // 延迟关闭窗口，让用户看到成功提示
  setTimeout(() => {
    handleClose();
  }, 1500); // 1.5秒后关闭
};

// 点击头像选择文件
const handleAvatarClick = () => {
  if (fileInput.value) {
    fileInput.value.click();
  }
};

// 处理文件选择
const handleFileChange = async (event) => {
  const file = event.target.files[0];
  if (!file) return;

  // 验证文件类型
  if (!file.type.startsWith('image/')) {
    ElMessage.warning('请选择图片文件');
    return;
  }

  // 验证文件大小（限制为5MB）
  const maxSize = 5 * 1024 * 1024; // 5MB
  if (file.size > maxSize) {
    ElMessage.warning('图片大小不能超过5MB');
    return;
  }

  try {
    // 显示上传中提示
    const loadingMessage = ElMessage({
      message: '头像上传中...',
      type: 'info',
      duration: 0 // 不自动关闭
    });

    // 调用上传接口，直接传递文件对象
    const { data } = await updateAvatar(file, fileTypeEnum.AVATAR);

    // 关闭加载提示
    loadingMessage.close();

    if (data) {
// 更新头像显示（data 是返回的图片URL）
      userAvatar.value = data;

      // 更新用户信息中的头像
      const updatedUserInfo = {
        ...userInfo.value,
        avatar: data
      };

      chatStore.setUserInfo(updatedUserInfo);
      localStorage.setItem('userInfo', JSON.stringify(updatedUserInfo));

      ElMessage.success('头像上传成功');
    } else {
      ElMessage.error('头像上传失败');
    }
  } catch (error) {
    console.error('头像上传失败:', error);
    ElMessage.error('头像上传失败，请重试');
  }

  // 清空文件选择器
  event.target.value = '';
};

// 关闭窗口
const handleClose = () => {
  if (window.electron && window.electron.ipcRenderer) {
    window.electron.ipcRenderer.send('close-profile-window');
  }
};
</script>

<style scoped>
.edit-profile {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  overflow: hidden;
}

.drag-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  color: white;
  flex-shrink: 0;
}

.title {
  font-size: 18px;
  font-weight: 600;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.close-btn {
  padding: 8px;
  border-radius: 50%;
  transition: all 0.2s ease;
  color: white !important;
}

.close-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

.content {
  flex: 1;
  background: white;
  margin: 0;
  border-radius: 0;
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30px 0 20px 0;
  position: relative;
  background: white;
}

.avatar {
  border: 3px solid #fff;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.avatar:hover {
  transform: scale(1.02);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
}

.form-section {
  background-color: white;
  padding: 20px 24px;
  flex: 1;
  overflow-y: auto;
}

.full-width {
  width: 100%;
}

.footer {
  display: flex;
  justify-content: center;
  gap: 20px;
  padding: 20px 24px;
  background: white;
  border-top: 1px solid #e9ecef;
  flex-shrink: 0;
}

.cancel-btn {
  min-width: 120px;
  height: 44px;
  border-radius: 22px;
  border: 2px solid #dee2e6;
  background: white;
  color: #6c757d;
  font-weight: 500;
  font-size: 14px;
  transition: all 0.3s ease;
}

.cancel-btn:hover {
  background: #f8f9fa;
  border-color: #adb5bd;
  color: #495057;
}

.save-btn {
  min-width: 120px;
  height: 44px;
  border-radius: 22px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  font-weight: 500;
  font-size: 14px;
  transition: all 0.3s ease;
}

.save-btn:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

/* 自定义 Element Plus 组件样式 */
:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #495057;
  font-size: 14px;
  line-height: 1.5;
}

:deep(.el-input__wrapper),
:deep(.el-textarea__inner),
:deep(.el-select .el-input__wrapper) {
  border-radius: 8px;
  border: 1px solid #dee2e6;
  box-shadow: none;
  transition: all 0.2s ease;
  background: #f8f9fa;
}

:deep(.el-input__wrapper:hover),
:deep(.el-textarea__inner:hover),
:deep(.el-select .el-input__wrapper:hover) {
  border-color: #adb5bd;
  background: white;
}

:deep(.el-input__wrapper.is-focus),
:deep(.el-textarea__inner:focus),
:deep(.el-select .el-input__wrapper.is-focus) {
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

:deep(.el-input__inner),
:deep(.el-textarea__inner) {
  font-size: 14px;
  color: #495057;
  background: transparent;
}

:deep(.el-scrollbar__bar) {
  opacity: 0.3;
  border-radius: 4px;
}

:deep(.el-scrollbar__bar:hover) {
  opacity: 0.6;
}

:deep(.el-scrollbar__thumb) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px;
}

/* 隐藏文本域调整大小图标 */
:deep(.el-textarea__inner) {
  resize: none !important;
}
</style>