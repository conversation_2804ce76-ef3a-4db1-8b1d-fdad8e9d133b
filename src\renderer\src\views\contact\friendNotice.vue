<script setup>
import { ref, onMounted } from 'vue'
import notice from '../../components/notice.vue'
import Base from '../../components/base.vue'
import AgreeDialog from '../../components/AgreeDialog.vue'
import { getUserInforms, rejectFriend } from '../../api/user'
import { datetimeCope } from '../../utils/global'
import { ElMessage } from 'element-plus'

const noticeList = ref([])

const applyStatus = ref({
  0: '申请中',
  1: '已同意',
  2: '已拒绝'
})

// 对话框状态
const showAgreeDialog = ref(false)
const currentUserInfo = ref({})

onMounted(async () => {
  const res = await getUserInforms()
  // 为每个通知项添加 showDropdown 属性
  noticeList.value = res.data.map((item) => ({
    ...item,
    showDropdown: false
  }))
})

// 处理同意好友申请
const handleAgree = async (item) => {
  currentUserInfo.value = item
  showAgreeDialog.value = true
}

// 处理拒绝好友申请
const handleReject = async (item) => {
  try {
    const userInformId = item.userInformId
    const { data } = await rejectFriend(userInformId)
    if (data) {
      ElMessage({
        showClose: true,
        message: '已拒绝好友申请',
        type: 'success'
      })
      // 更新通知状态
      item.status = 2
    } else {
      ElMessage({
        showClose: true,
        message: '操作失败',
        type: 'error'
      })
    }
  } catch (error) {
    console.error('拒绝好友申请失败:', error)
    ElMessage({
      showClose: true,
      message: '操作失败，请重试',
      type: 'error'
    })
  }
  // 隐藏下拉菜单
  item.showDropdown = false
}

// 切换下拉菜单显示状态
const toggleDropdown = (item) => {
  // 先关闭所有其他的下拉菜单
  noticeList.value.forEach((notice) => {
    if (notice !== item) {
      notice.showDropdown = false
    }
  })
  // 切换当前项的下拉菜单
  item.showDropdown = !item.showDropdown
}

// 关闭同意对话框
const handleCloseDialog = () => {
  showAgreeDialog.value = false
  currentUserInfo.value = {}
}

// 同意成功后的处理
const handleAgreeSuccess = () => {
  // 更新当前用户的状态
  const targetItem = noticeList.value.find(
    (item) => item.userInformId === currentUserInfo.value.userInformId
  )
  if (targetItem) {
    targetItem.status = 1
  }
}
</script>

<template>
  <div class="friend-notice">
    <div class="notice-header">
      <Base />
    </div>
    <div class="title-bar">
      <div class="left">好友通知</div>
    </div>
    <div class="notice-content">
      <div v-for="item in noticeList" :key="item.userInformId" class="notice-item">
        <notice>
          <template #photo>
            <img :src="item.avatar" alt="" class="avatar" />
          </template>
          <template #friend-name>
            <div class="info-container">
              <div class="name">{{ item.nickname }}</div>
              <div class="request-and-time">
                <span class="request-text">{{
                  Number(item.joinType) === 0 ? '请求加为好友' : '正在验证你的好友申请'
                }}</span>
                <span class="time">{{ datetimeCope(item.applyTime) }}</span>
              </div>
            </div>
          </template>
          <template #message>
            <div class="message-content">
              <span class="label">留言：</span>
              <span class="text">{{ item.leaveWord }}</span>
            </div>
          </template>
          <template #action>
            <div
              v-if="Number(item.status) === 0 && Number(item.joinType) === 0"
              class="action-buttons"
            >
              <el-button
                class="agree-btn"
                size="small"
                type="primary"
                plain
                @click="handleAgree(item)"
              >
                同意
              </el-button>
              <div class="dropdown-wrapper" :class="{ active: item.showDropdown }">
                <button class="dropdown-trigger" @click="toggleDropdown(item)">
                  <svg class="icon" aria-hidden="true">
                    <use xlink:href="#icon-xiajiantou"></use>
                  </svg>
                </button>
                <div v-show="item.showDropdown" class="dropdown-menu">
                  <button class="dropdown-item reject-btn" @click="handleReject(item)">拒绝</button>
                </div>
              </div>
            </div>
            <el-button v-else class="agree-btn disabled" size="small" type="default" plain disabled>
              {{ applyStatus[item.status] }}
            </el-button>
          </template>
        </notice>
      </div>
    </div>

    <!-- 同意好友对话框 -->
    <AgreeDialog
      :visible="showAgreeDialog"
      :user-info="currentUserInfo"
      @close="handleCloseDialog"
      @success="handleAgreeSuccess"
    />
  </div>
</template>

<style lang="scss" scoped>
.friend-notice {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  margin-top: -30px;
  overflow: hidden;
}

.notice-header {
  height: 30px;
  -webkit-app-region: drag;
  position: relative;
  z-index: 1;
}

.notice-content {
  flex: 1;
  background: #f5f6f7;
  padding: 0;
  overflow-y: auto;
  overflow-x: hidden;
  margin-top: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
}

.notice-item {
  margin-bottom: 12px;
  width: 100%;
  max-width: 600px;
  padding: 0 12px;
  box-sizing: border-box;
}

.avatar {
  width: 40px;
  height: 40px;
  object-fit: cover;
}

.info-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 8px;
  margin-top: 8px;
}

.name {
  font-size: 12px;
  font-weight: 500;
  color: #409eff;
  margin-right: 5px;
}

.request-and-time {
  display: flex;
  align-items: center;

  .request-text {
    font-size: 12px;
    color: #000;
    margin-right: 8px;
  }

  .time {
    font-size: 12px;
    color: #999;
  }
}

.message-content {
  font-size: 12px;
  margin-left: 8px;

  .label,
  .text {
    color: #666;
  }
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 0;
}

.agree-btn {
  padding: 4px 12px;
  font-size: 12px;
  height: 28px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border: none !important;
  transition: all 0.2s ease;

  &:hover:not(.disabled) {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
  }

  &:active:not(.disabled) {
    transform: translateY(0);
  }

  &.disabled {
    color: #999;
    background: #f5f5f5;
    border: none !important;
    cursor: not-allowed;
  }
}

.dropdown-wrapper {
  position: relative;
  display: inline-block;

  .dropdown-trigger {
    height: 28px;
    width: 24px;
    border: none;
    background-color: #ecf5ff;
    cursor: pointer;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;

    &:hover {
      background: #409eff;
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
    }

    &:active {
      transform: translateY(0);
    }

    .icon {
      color: #409eff;
      font-size: 12px;
      transition: transform 0.2s;
    }
  }

  &.active .dropdown-trigger {
    background: #ecf5ff;

    .icon {
      transform: rotate(180deg);
    }
  }

  .dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: #fff;
    border: none;
    border-radius: 6px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    min-width: 60px;
    margin-top: 4px;
    overflow: hidden;
    animation: dropdownFadeIn 0.2s ease-out;

    .dropdown-item {
      display: block;
      width: 100%;
      padding: 8px 12px;
      border: none;
      background: none;
      text-align: center;
      cursor: pointer;
      font-size: 12px;
      color: #606266;
      transition: all 0.2s ease;
      position: relative;

      &:hover {
        background: #f8f9fa;
        transform: translateY(-1px);
      }

      &.reject-btn {
        color: #f56c6c;
        font-weight: 500;

        &:hover {
          background: linear-gradient(135deg, #fef0f0 0%, #fde2e2 100%);
          color: #e53e3e;
          transform: translateY(-1px);
        }

        &:active {
          transform: translateY(0);
        }
      }
    }
  }

  @keyframes dropdownFadeIn {
    from {
      opacity: 0;
      transform: translateY(-8px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }
}

.title-bar {
  margin-top: 10px;
  height: 40px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;

  .left {
    font-size: 16px;
    color: #000;
    font-weight: 500;
  }

  .right {
    display: flex;
    gap: 16px;

    .iconfont {
      font-size: 16px;
      color: #666;
      cursor: pointer;

      &:hover {
        color: #333;
      }
    }
  }
}
</style>
