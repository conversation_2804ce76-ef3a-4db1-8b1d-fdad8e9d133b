<script setup>
import { ref, onMounted } from 'vue'
import notice from '../../components/notice.vue'
import Base from '../../components/base.vue'
import { getGroupNoticeInfo, agreeJoinGroup } from '../../api/group'
import { useChatStore } from '../../store/chatStore'
import { msgDatetimeCope, joinGroupStatusEnum } from '../../utils/global'
import { ElMessage } from 'element-plus'

const chatStore = useChatStore()
const userInfo = ref(chatStore.getUserInfo())
const userId = Number(userInfo.value.userId)
const noticeType = ref(chatStore.noticeType)

const noticeList = ref([])

// 处理同意操作
const handleAgree = async (item) => {
  // 检查当前状态，如果已经处理过则不执行操作
  if (Number(item.joinGroupStatus) === joinGroupStatusEnum.agree) {
    console.log('该申请已经同意过了')
    return
  }
  if (Number(item.joinGroupStatus) === joinGroupStatusEnum.reject) {
    console.log('该申请已经被拒绝，无法再同意')
    return
  }

  const { data } = await agreeJoinGroup({
    groupNoticeId: item.groupNoticeId,
    operatorId: userId,
    joinGroupStatus: joinGroupStatusEnum.agree
  })
  if (!data) {
    ElMessage({
      type: 'error',
      message: '同意加群失败'
    })
  }
  ElMessage({
    type: 'success',
    message: '同意加群成功'
  })
}

// 处理拒绝操作
const handleReject = (item) => {
  console.log('拒绝操作:', item)
  // 这里可以添加具体的拒绝逻辑
  item.showDropdown = false
}

// 切换下拉菜单显示状态
const toggleDropdown = (item) => {
  // 先关闭所有其他的下拉菜单
  noticeList.value.forEach((notice) => {
    if (notice !== item) {
      notice.showDropdown = false
    }
  })
  // 切换当前项的下拉菜单状态
  item.showDropdown = !item.showDropdown
}

onMounted(async () => {
  const { data } = await getGroupNoticeInfo()
  noticeList.value = data
})
</script>

<template>
  <div class="union-notice">
    <div class="notice-header">
      <Base />
    </div>
    <div class="title-bar">
      <div class="left">群通知</div>
      <div class="right">
        <i class="iconfont icon-shaixuan"></i>
        <span>筛</span>
      </div>
    </div>
    <div class="notice-content">
      <div v-for="item in noticeList" :key="item.id" class="notice-item">
        <notice>
          <template #photo>
            <img
              v-if="
                (Number(item.operatorId) !== userId && item.noticeType === noticeType.quit) ||
                Number(item.targetUserId) !== userId
              "
              :src="item.opeAvatar"
              class="avatar"
            />
            <img v-else :src="item.groupAvatar" class="avatar" />
          </template>
          <template #friend-name>
            <div class="info-container">
              <div
                v-if="Number(item.operatorId) !== userId && item.noticeType === noticeType.quit"
                class="name"
              >
                {{ item.opeNickname }}
              </div>
              <div v-else class="name">{{ item.groupNickname }}</div>
              <div class="time">{{ msgDatetimeCope(item.createTime) }}</div>
            </div>
          </template>
          <template #message>
            <div class="message-content">
              <div class="message-line">
                <template
                  v-if="Number(item.operatorId) !== userId && item.noticeType === noticeType.quit"
                >
                  <span class="message-text">退出</span>
                  <span class="target-user">{{ item.groupName }}</span>
                </template>
                <template v-else>
                  <template v-if="item.noticeType === noticeType.join">
                    <span class="target-user">{{ item.targetNickname }}</span>
                    <span class="message-text">申请加群</span>
                  </template>
                  <template v-else-if="item.noticeType === noticeType.setAdmin">
                    <span class="message-text">已将</span>
                    <span class="target-user">{{ item.targetNickname }}</span>
                    <span class="message-text">设置为管理员</span>
                  </template>
                  <template v-else>
                    <span class="target-user">{{ item.targetNickname }}</span>
                    <span class="message-text">已被踢出群聊</span>
                  </template>
                </template>
              </div>
              <div
                v-if="item.noticeType !== noticeType.quit && item.opeNickname !== null"
                class="handler"
              >
                <span class="handler-label">处理人：</span>
                <span class="group-name">{{ item.opeNickname }}</span>
              </div>
            </div>
          </template>
          <template #action>
            <div v-if="item.noticeType === noticeType.join" class="action-buttons">
              <el-button
                class="agree-btn"
                :class="{
                  disabled:
                    Number(item.joinGroupStatus) === joinGroupStatusEnum.agree ||
                    Number(item.joinGroupStatus) === joinGroupStatusEnum.reject
                }"
                size="small"
                type="primary"
                plain
                :disabled="
                  Number(item.joinGroupStatus) === joinGroupStatusEnum.agree ||
                  Number(item.joinGroupStatus) === joinGroupStatusEnum.reject
                "
                @click="handleAgree(item)"
              >
                <span v-if="Number(item.joinGroupStatus) === joinGroupStatusEnum.agree">
                  已同意
                </span>
                <span v-else-if="Number(item.joinGroupStatus) === joinGroupStatusEnum.reject">
                  已拒绝
                </span>
                <span v-else>同意</span>
              </el-button>
              <div
                class="dropdown-wrapper"
                :class="{
                  active: item.showDropdown,
                  disabled:
                    Number(item.joinGroupStatus) === joinGroupStatusEnum.agree ||
                    Number(item.joinGroupStatus) === joinGroupStatusEnum.reject
                }"
              >
                <button
                  class="dropdown-trigger"
                  :disabled="
                    Number(item.joinGroupStatus) === joinGroupStatusEnum.agree ||
                    Number(item.joinGroupStatus) === joinGroupStatusEnum.reject
                  "
                  @click="toggleDropdown(item)"
                >
                  <svg class="icon" aria-hidden="true">
                    <use xlink:href="#icon-xiajiantou"></use>
                  </svg>
                </button>
                <div v-show="item.showDropdown" class="dropdown-menu">
                  <button class="dropdown-item reject-btn" @click="handleReject(item)">拒绝</button>
                </div>
              </div>
            </div>
          </template>
        </notice>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.union-notice {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  margin-top: -30px;
  overflow: hidden;
}

.notice-header {
  height: 30px;
  -webkit-app-region: drag;
  position: relative;
  z-index: 1;
}

.title-bar {
  margin-top: 10px;
  height: 40px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;

  .left {
    font-size: 16px;
    color: #000;
    font-weight: 500;
  }

  .right {
    display: flex;
    align-items: center;
    gap: 4px;
    color: #666;
    cursor: pointer;
    font-size: 14px;

    &:hover {
      color: #333;
    }

    .iconfont {
      font-size: 16px;
    }
  }
}

.notice-content {
  flex: 1;
  background: #f5f6f7;
  padding: 0;
  overflow-y: auto;
  overflow-x: hidden;
  margin-top: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
}

.notice-item {
  margin-bottom: 12px;
  width: 100%;
  max-width: 600px;
  padding: 0 12px;
  box-sizing: border-box;
}

.avatar {
  width: 40px;
  height: 40px;
  object-fit: cover;
  border-radius: 50%;
}

.info-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-left: 8px;
  margin-top: 8px;
}

.name {
  font-size: 12px;
  font-weight: 500;
  color: #409eff;
}

.time {
  margin-left: 10px;
  font-size: 12px;
  color: #999;
  white-space: nowrap;
}

.message-content {
  margin-left: 8px;
  font-size: 12px;
  color: #666;
}

.message-line {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.message-text {
  color: #333;
  font-size: 12px;
}

.target-user {
  color: #409eff;
  font-size: 12px;
  margin: 0 2px;
}

.group-name {
  font-size: 12px;
  color: #409eff;
}

.handler {
  display: flex;
  align-items: center;

  .handler-label {
    color: #666;
    font-size: 12px;
  }

  .handler-name {
    color: #409eff;
    cursor: pointer;
    font-size: 12px;

    &:hover {
      text-decoration: underline;
    }
  }
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 0;
}

.agree-btn {
  padding: 4px 12px;
  font-size: 12px;
  height: 28px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border: none !important;
  transition: all 0.2s ease;

  &:hover:not(.disabled) {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
  }

  &:active:not(.disabled) {
    transform: translateY(0);
  }

  &.disabled {
    color: #999;
    background: #f5f5f5;
    border: none !important;
    cursor: not-allowed;
  }
}

.dropdown-wrapper {
  position: relative;
  display: inline-block;

  .dropdown-trigger {
    height: 28px;
    width: 24px;
    border: none;
    background-color: #ecf5ff;
    cursor: pointer;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;

    &:hover:not(:disabled) {
      background: #409eff;
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
    }

    &:active:not(:disabled) {
      transform: translateY(0);
    }

    &:disabled {
      background: #f5f5f5;
      cursor: not-allowed;

      .icon {
        color: #c0c4cc;
      }
    }

    .icon {
      color: #409eff;
      font-size: 12px;
      transition: transform 0.2s;
    }
  }

  &.disabled {
    .dropdown-trigger {
      background: #f5f5f5;
      cursor: not-allowed;

      &:hover {
        background: #f5f5f5;
        transform: none;
        box-shadow: none;
      }

      .icon {
        color: #c0c4cc;
      }
    }
  }

  &.active .dropdown-trigger {
    background: #ecf5ff;

    .icon {
      transform: rotate(180deg);
    }
  }

  .dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: #fff;
    border: none;
    border-radius: 6px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    min-width: 60px;
    margin-top: 4px;
    overflow: hidden;
    animation: dropdownFadeIn 0.2s ease-out;

    .dropdown-item {
      display: block;
      width: 100%;
      padding: 8px 12px;
      border: none;
      background: none;
      text-align: center;
      cursor: pointer;
      font-size: 12px;
      color: #606266;
      transition: all 0.2s ease;
      position: relative;

      &:hover {
        background: #f8f9fa;
        transform: translateY(-1px);
      }

      &.reject-btn {
        color: #f56c6c;
        font-weight: 500;

        &:hover {
          background: linear-gradient(135deg, #fef0f0 0%, #fde2e2 100%);
          color: #e53e3e;
          transform: translateY(-1px);
        }

        &:active {
          transform: translateY(0);
        }
      }
    }
  }
}

@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
