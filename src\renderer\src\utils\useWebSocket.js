import { ref } from 'vue'
import { useChatStore } from '../store/chatStore'

export function useWebSocket(url) {
  const socket = ref(null)
  const messages = ref([])
  const chatStore = useChatStore()
  const heartBeatInterval = ref(null)  // 心跳发送定时器
  const missedAcks = ref(0) // 未收到响应的次数
  const reconnectInterval = ref(null)  // 重连定时器

  // 连接 WebSocket，返回Promise
  const connect = () => {
    return new Promise((resolve, reject) => {
      socket.value = new WebSocket(url)

      socket.value.onopen = () => {
        console.log('WebSocket连接已建立')
        if (reconnectInterval.value !== null) {
          clearInterval(reconnectInterval.value)
        }
        // 每10s发送心跳
        heartBeatInterval.value = setInterval(() => {
          if (socket.value.readyState === WebSocket.OPEN) {
            socket.value.send('ping')
            missedAcks.value++
            // 连续2次未收到响应 则重连
            if (missedAcks.value >= 2) {
              reconnect()
            }
          }
        }, 10000)
        resolve(socket.value) // 连接成功时resolve
      }

      // 接收到消息 todo 会不会有并发问题
      socket.value.onmessage = (event) => {
        if (event.data === 'pong') {
          missedAcks.value = 0
          return
        }

        const data = JSON.parse(event.data)
        if (data?.code === '666') {
          // todo 消息发送失败，和chatStore.sendMessage(JSON.stringify(data))抛异常一样处理
        } else if (data.chatSession) {
          // 收到消息，更新消息列表
          const chatSession = data.chatSession
          const messages = data.messages
          const messageVO = data.messageVO
          if (chatStore.existChatSession(chatSession)) {
            //存在会话列表
            if (messages !== null) {
              chatStore.updateChatSessionOne(chatSession, messages)
            } else {
              chatStore.updateChatSessionOne(chatSession, messageVO)
            }
            if (chatSession.chatSessionId === chatStore.getActiveChatSessionId()) {
              // 当前激活的会话，更新消息列表
              if (messages !== null) {
                chatStore.setMessages(chatSession.chatSessionId, messages)
              } else {
                chatStore.setMessagesVO(chatSession.chatSessionId, messageVO)
              }
            }
          } else {
            // 私聊
            if (messages !== null) {
              chatStore.setChatSessionOne(chatSession)
            } else {
              // 群聊
              chatStore.setChatSessionOne(chatSession, messageVO.nickname)
            }
          }
        }
      }

      socket.value.onclose = () => {
        console.log('WebSocket连接已关闭')
      }

      socket.value.onerror = (error) => {
        console.error('WebSocket错误:', error)
        reject(error) // 连接失败时reject
      }
    })
  }

  // 重连
  const reconnect = () => {
    clearInterval(heartBeatInterval.value)
    socket.value.close();
    reconnectInterval.value = setInterval(() => {
      connect()
    }, 2000)
  }

  // 发送消息
  const sendMessage = (message) => {
    if (socket.value && socket.value.readyState === WebSocket.OPEN) {
      socket.value.send(message)
      console.log('发送消息:', message)
    } else {
      console.error('WebSocket未连接')
    }
  }

  // 立即连接 WebSocket
  const connectionPromise = connect()

  return { socket, messages, sendMessage, connect, connectionPromise }
}
