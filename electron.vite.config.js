import { defineConfig } from 'electron-vite'
import vue from '@vitejs/plugin-vue'

export default defineConfig({
  main: {
    build: {
      rollupOptions: {
        external: ['@electron-toolkit/utils']
      }
    }
  },
  preload: {
    build: {
      rollupOptions: {
        external: ['electron']
      }
    }
  },
  renderer: {
    server: {
      proxy: {
        '/api': {
          target: 'http://localhost:8101',
          changeOrigin: true
        }
      }
    },
    plugins: [vue()]
  }
}) 