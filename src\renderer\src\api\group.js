import Axios from './http'

// 全网搜索群聊
export const searchGroups = (keyword) => {
  return Axios.get('/group/queryGroups', { params: { keyword } })
}

// 申请加群
export const addGroup = (data) => {
  return Axios.post('/groupNotice/add/notice', data)
}

// 获取群通知信息
export const getGroupNoticeInfo = () => {
  return Axios.get('/groupNotice/list')
}

// 同意加群
export const agreeJoinGroup = (data) => {
  return Axios.post('/groupNotice/agree', data)
}
