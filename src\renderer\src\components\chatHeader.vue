<template>
  <div id="chatHeader">
    <div class="chat-header">
      <div class="chat-info">
        <div class="chat-details">
          <div class="chat-name">
            {{ chatInfo?.name || '加载中...' }}
            {{
              chatInfo?.memberCount === null || chatInfo?.memberCount === undefined
                ? ''
                : '(' + chatInfo.memberCount + ')'
            }}
          </div>
        </div>
      </div>
      <div class="chat-actions">
        <button class="action-btn">
          <svg class="icon" aria-hidden="true">
            <use xlink:href="#icon-dianhua"></use>
          </svg>
        </button>
        <button class="action-btn">
          <svg class="icon" aria-hidden="true">
            <use xlink:href="#icon-shipintonghua"></use>
          </svg>
        </button>
        <button class="action-btn">
          <svg class="icon" aria-hidden="true">
            <use xlink:href="#icon-wendang"></use>
          </svg>
        </button>
        <button class="action-btn">
          <svg class="icon" aria-hidden="true">
            <use xlink:href="#icon-applications"></use>
          </svg>
        </button>
        <button class="action-btn">
          <svg class="icon" aria-hidden="true">
            <use xlink:href="#icon-yaoqinghaoyou"></use>
          </svg>
        </button>
        <button class="action-btn">
          <svg class="icon" aria-hidden="true">
            <use xlink:href="#icon-sangedian"></use>
          </svg>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineModel } from 'vue'

const chatInfo = defineModel({ required: true }) //自定绑定modelValue
</script>

<style scoped>
#chatHeader {
  .chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 16px; /* 进一步减少垂直内边距 */
    padding-top: 28px; /* 减少顶部间距 */
    background-color: #f2f2f2;
    border-bottom: 1px solid #e0e0e0;
    min-height: 40px; /* 减少最小高度 */

    .chat-info {
      display: flex;
      align-items: center;

      .chat-avatar {
        width: 32px;
        height: 32px;
        border-radius: 4px;
        margin-right: 12px;
      }

      .chat-name {
        font-size: 16px;
        font-weight: 500;
        color: #333;
      }
    }

    .chat-actions {
      display: flex;
      align-items: center;
      gap: 6px;

      .action-btn {
        width: 30px;
        height: 30px;
        border: none;
        background: transparent;
        cursor: pointer;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.15s ease;
        padding: 0;

        &:hover {
          background-color: rgba(0, 0, 0, 0.05); /* 更轻微的悬停效果 */
        }

        &:active {
          background-color: rgba(0, 0, 0, 0.08);
          transform: scale(0.96); /* 轻微缩放效果 */
        }

        .icon {
          width: 18px; /* 增大图标尺寸 */
          height: 18px;
          fill: #666;
          pointer-events: none;
        }
      }
    }
  }
}
</style>
