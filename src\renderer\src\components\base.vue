<script setup>
import { ref, onMounted } from 'vue'

const windowControlsRef = ref(null)
const dynamicBackgroundColor = ref('transparent')

const handleMinimize = () => {
  window.electron.ipcRenderer.send('minimize-window')
}

const handleMaximize = () => {
  window.electron.ipcRenderer.send('maximize-window')
}

const handleClose = () => {
  window.electron.ipcRenderer.send('close-window')
}

// 检测页面背景色并设置匹配的背景色
const detectAndSetBackground = () => {
  try {
    // 获取窗口控制按钮下方的元素
    const elementBelow = document.elementFromPoint(window.innerWidth - 50, 50)
    if (elementBelow) {
      const computedStyle = window.getComputedStyle(elementBelow)
      const backgroundColor = computedStyle.backgroundColor

      // 如果检测到的背景色不是透明的，使用该颜色
      if (backgroundColor && backgroundColor !== 'rgba(0, 0, 0, 0)' && backgroundColor !== 'transparent') {
        dynamicBackgroundColor.value = backgroundColor
      } else {
        // 尝试检测父元素的背景色
        let parent = elementBelow.parentElement
        while (parent && parent !== document.body) {
          const parentStyle = window.getComputedStyle(parent)
          const parentBg = parentStyle.backgroundColor
          if (parentBg && parentBg !== 'rgba(0, 0, 0, 0)' && parentBg !== 'transparent') {
            dynamicBackgroundColor.value = parentBg
            break
          }
          parent = parent.parentElement
        }
      }
    }
  } catch (error) {
    console.log('背景色检测失败，使用默认透明背景')
  }
}

onMounted(() => {
  // 初始检测
  setTimeout(detectAndSetBackground, 100)

  // 监听路由变化或页面更新
  const observer = new MutationObserver(() => {
    setTimeout(detectAndSetBackground, 50)
  })

  observer.observe(document.body, {
    childList: true,
    subtree: true,
    attributes: true,
    attributeFilter: ['style', 'class']
  })
})
</script>

<template>
  <div class="window-controls" :style="{ backgroundColor: dynamicBackgroundColor }">
    <div class="icon minimize" @click="handleMinimize">
      <svg class="outerColor" aria-hidden="true">
        <use xlink:href="#icon-hengxian"></use>
      </svg>
    </div>
    <div class="icon maximize" @click="handleMaximize">
      <svg class="outerColor" aria-hidden="true">
        <use xlink:href="#icon-fangkuang"></use>
      </svg>
    </div>
    <div class="icon close" @click="handleClose">
      <svg class="outerColor" aria-hidden="true">
        <use xlink:href="#icon-shanchu"></use>
      </svg>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.window-controls {
  position: fixed;
  top: 0;
  right: 0;
  display: flex;
  align-items: center;
  height: 30px;
  z-index: 9999;
  -webkit-app-region: no-drag;
  border-bottom-left-radius: 3px;

  .icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
    background: transparent;
    position: relative;

    &:hover {
      background-color: rgba(0, 0, 0, 0.1);
      color: #333;
    }

    &:active {
      background-color: rgba(0, 0, 0, 0.15);
      transform: scale(0.95);
    }

    /* SVG 图标样式 */
    .outerColor {
      width: 12px;
      height: 12px;
      fill: currentColor;
      pointer-events: none;
      transition: fill 0.2s ease;
    }

    /* 确保use元素继承颜色 */
    use {
      fill: inherit;
    }
  }

  /* 最小化按钮样式 */
  .minimize {
    &:hover {
      background-color: rgba(0, 0, 0, 0.08);
      color: #333;
    }
  }

  /* 最大化按钮样式 */
  .maximize {
    &:hover {
      background-color: rgba(0, 0, 0, 0.08);
      color: #333;
    }
  }

  /* 关闭按钮样式 */
  .close {
    &:hover {
      background-color: #e81123;
      color: #fff;
    }

    &:active {
      background-color: #c50e1f;
      color: #fff;
    }
  }
}
</style>
