<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { getFriendGroups, getGroupFriends } from '../api/user'
import { statusList } from '../utils/status'
import FriendInfo from './contact/FriendInfo.vue'
import { getPinnedGroups, getCreatedGroups, getManageGroups, getJoinGroups } from '../api/user'

const router = useRouter()
const input4 = ref('')
const activeTab = ref('friend') // 当前活跃tab
const showAddOptions = ref(false) // 控制加号选项的显示

// 点击其他地方关闭选项框
const handleClickOutside = (e) => {
  const addOptions = document.querySelector('.add-options')
  const addIcon = document.querySelector('.jiajia')
  if (addOptions && !addOptions.contains(e.target) && !addIcon.contains(e.target)) {
    showAddOptions.value = false
  }
}

// 获取好友分组列表
const friendGroups = ref([])
const getFriendGroupsGet = async () => {
  const data = await getFriendGroups()
  friendGroups.value = data.data
}

// 群组信息
const pinnedGroups = ref([]) // 置顶群聊
const createdGroups = ref([]) // 我创建的群聊
const manageGroups = ref([]) // 我管理的群聊
const joinGroups = ref([]) // 我加入的群聊

// 添加全局点击事件监听
onMounted(async () => {
  document.addEventListener('click', handleClickOutside)
  getFriendGroupsGet()

  // 获取所有群组信息
  const pinnedGroupsData = await getPinnedGroups()
  pinnedGroups.value = pinnedGroupsData.data
  const createdGroupsData = await getCreatedGroups()
  createdGroups.value = createdGroupsData.data
  const manageGroupsData = await getManageGroups()
  manageGroups.value = manageGroupsData.data
  const joinGroupsData = await getJoinGroups()
  joinGroups.value = joinGroupsData.data
  console.log(pinnedGroups.value)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

// 好友分组点击
const activeGroups = ref([]) // 活跃分组id列表
const friends = ref({}) // 好友列表，key为分组id，value为分组好友列表
const selectedFriendId = ref(null) // 选中的好友ID
const selectedFriendGroup = ref('') // 选中好友的分组名称
const groupMembers = async (groupFriendId) => {
  // 活跃分组中存在groupFriendId
  if (activeGroups.value.includes(groupFriendId)) {
    activeGroups.value = activeGroups.value.filter((item) => item !== groupFriendId)
    delete friends.value[groupFriendId] // 从friends对象中删除groupFriendId对应的分组好友列表
    return
  }

  const data = await getGroupFriends(groupFriendId)
  friends.value[groupFriendId] = data.data
  // 将状态和图标对应起来
  friends.value[groupFriendId].forEach((item) => {
    const statusItem = statusList.find((status) => status.text === item.status)
    item.icon = statusItem ? statusItem.icon : 'icon-lixian' // 找不到匹配状态，则离线
  })
  activeGroups.value.push(groupFriendId) // 将groupFriendId添加到活跃分组列表中
}

// 查看好友详情
const detailFriend = (userId, groupName) => {
  selectedFriendId.value = userId
  selectedFriendGroup.value = groupName
  console.log('查看好友详情', userId, '分组:', groupName)
}

// 好友信息操作事件处理
const handleSendMessage = (friendInfo) => {
  console.log('发送消息给:', friendInfo)
  // TODO: 跳转到聊天页面
}

const handleAudioCall = (friendInfo) => {
  console.log('语音通话:', friendInfo)
  // TODO: 发起语音通话
}

const handleVideoCall = (friendInfo) => {
  console.log('视频通话:', friendInfo)
  // TODO: 发起视频通话
}

// 处理好友分组变更
const handleGroupChanged = async (changeInfo) => {
  const { oldGroupId, oldGroupName, newGroupId, newGroupName, friendId } = changeInfo

  // 更新当前选中好友的分组信息
  selectedFriendGroup.value = newGroupName

  // 检查新分组是否在活跃分组列表中
  const isNewGroupActive = activeGroups.value.includes(newGroupId)
  // 检查旧分组是否在活跃分组列表中
  const isOldGroupActive = activeGroups.value.includes(oldGroupId)

  if (isNewGroupActive && isOldGroupActive) {
    // 情况2：新旧分组都在活跃列表中，需要更新两个分组的成员
    const [oldGroupData, newGroupData] = await Promise.all([
      getGroupFriends(oldGroupId),
      getGroupFriends(newGroupId)
    ])
    friends.value[oldGroupId] = oldGroupData.data
    friends.value[newGroupId] = newGroupData.data

    // 为新获取的好友数据添加状态图标
    friends.value[oldGroupId].forEach((item) => {
      const statusItem = statusList.find((status) => status.text === item.status)
      item.icon = statusItem ? statusItem.icon : 'icon-lixian'
    })
    friends.value[newGroupId].forEach((item) => {
      const statusItem = statusList.find((status) => status.text === item.status)
      item.icon = statusItem ? statusItem.icon : 'icon-lixian'
    })
  } else if (isOldGroupActive) {
    // 情况1：只有旧分组在活跃列表中，只需要更新旧分组的成员
    const oldGroupData = await getGroupFriends(oldGroupId)
    friends.value[oldGroupId] = oldGroupData.data

    // 为新获取的好友数据添加状态图标
    friends.value[oldGroupId].forEach((item) => {
      const statusItem = statusList.find((status) => status.text === item.status)
      item.icon = statusItem ? statusItem.icon : 'icon-lixian'
    })
  }

  console.log('分组变更处理完成:', changeInfo)
}
</script>

<script>
export default {
  components: {
    FriendInfo
  }
}

// 置顶群聊
const activePinnedGroups = ref(false)
// 我创建的群聊
const activeCreatedGroups = ref(false)
// 我管理的群聊
const activeManageGroups = ref(false)
// 我加入的群聊
const activeJoinGroups = ref(false)

// 处理选项点击
const handleOptionClick = (option) => {
  showAddOptions.value = false
  // 这里可以添加具体的处理逻辑
  console.log('选择了:', option)
}

// 处理通知点击
const handleNoticeClick = async (option) => {
  if (option === 'friend') {
    router.push('/home/<USER>/friend-notice')
  } else if (option === 'union') {
    router.push('/home/<USER>/union-notice')
  }
}
</script>

<template>
  <div class="contact">
    <div class="drag"></div>
    <div class="container">
      <div class="left">
        <div class="title">
          <el-input v-model="input4" style="width: 220px; margin-left: 10px; height: 28px" placeholder="搜索">
            <template #prefix>
              <i class="iconfont icon-sousuo"></i>
            </template>
          </el-input>
          <div class="add-button">
            <i class="jiajia iconfont icon-jiajia" @click.stop="showAddOptions = !showAddOptions"></i>
            <!-- 添加选项框 -->
            <div v-show="showAddOptions" class="add-options">
              <div class="option-item" @click="handleOptionClick('group')">
                <i class="iconfont icon-qunliao"></i>
                <span>发起群聊</span>
              </div>
              <div class="option-item" @click="handleOptionClick('friend')">
                <i class="iconfont icon-haoyou"></i>
                <span>加好友/群</span>
              </div>
            </div>
          </div>
        </div>
        <div class="notice">
          <div class="friendNotice" @click="handleNoticeClick('friend')">
            好友通知
            <i class="iconfont icon-youjiantou"></i>
          </div>
          <div class="friendNotice" @click="handleNoticeClick('union')">
            群通知
            <i class="iconfont icon-youjiantou"></i>
          </div>
        </div>
        <div class="option">
          <div :class="{ active: activeTab === 'friend' }" @click="activeTab = 'friend'">好友</div>
          <div :class="{ active: activeTab === 'union' }" @click="activeTab = 'union'">群聊</div>
        </div>
        <div class="slider" :style="{ transform: activeTab === 'friend' ? 'translateX(0)' : 'translateX(100%)' }"></div>
        <div v-if="activeTab === 'friend'" class="friendGroup">
          <template v-if="friendGroups.length > 0">
            <div v-for="item in friendGroups" :key="item.groupFriendId" class="group"
              @click="groupMembers(item.groupFriendId)">
              <svg class="icon youjiantou" aria-hidden="true">
                <use
                  :xlink:href="`#${activeGroups.includes(item.groupFriendId) ? 'icon-xiajiantou' : 'icon-youjiantou'}`">
                </use>
              </svg>
              <span class="groupName">{{ item.groupName }}</span>
              <span class="count group-count">{{ item.manCount }}/{{ item.manCount }}</span>
              <div v-if="activeGroups.includes(item.groupFriendId)" class="friends">
                <div v-for="item1 in friends[item.groupFriendId]" :key="item1.userId" class="friend"
                  @click.stop="detailFriend(item1.userId, item.groupName)">
                  <img src="../assets/icon.png" alt="暂无头像" class="avatar" />
                  <div class="info">
                    <span class="remark">{{ item1.remark }}</span>
                    <span class="status">
                      <span class="bracket">[</span>
                      <svg class="icon" aria-hidden="true">
                        <use :xlink:href="`#${item1.icon}`"></use>
                      </svg>
                      <span class="status-text">{{ item1.status }}</span>
                      <span class="bracket">]</span>
                      <span class="signiture">
                        {{ item1.signiture }}
                      </span>
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </template>
          <template v-else>暂无好友分组</template>
        </div>
        <div v-if="activeTab === 'union'" class="unionGroup">
          <div class="group" @click="activePinnedGroups = !activePinnedGroups">
            <svg class="icon youjiantou" aria-hidden="true">
              <use xlink:href="#icon-youjiantou"></use>
            </svg>
            <span>置顶群聊</span>
            <span class="count group-count">0</span>
          </div>
          <div v-if="activePinnedGroups">
            <div v-for="item in pinnedGroups" :key="item.groupId" class="groupData">
              <img class="avatar" src="../assets/icon.png" alt="暂无头像" />
              <span class="groupName">{{ item.groupName }}</span>
            </div>
          </div>
          <div class="group" @click="activeCreatedGroups = !activeCreatedGroups">
            <svg class="icon youjiantou" aria-hidden="true">
              <use xlink:href="#icon-youjiantou"></use>
            </svg>
            <span>我创建的群聊</span>
            <span class="count group-count">0</span>
          </div>
          <div v-if="activeCreatedGroups">
            <div v-for="item in createdGroups" :key="item.groupId" class="groupData">
              <img class="avatar" src="../assets/icon.png" alt="暂无头像" />
              <span class="groupName">{{ item.groupName }}</span>
            </div>
          </div>
          <div class="group" @click="activeManageGroups = !activeManageGroups">
            <svg class="icon youjiantou" aria-hidden="true">
              <use xlink:href="#icon-youjiantou"></use>
            </svg>
            <span>我管理的群聊</span>
            <span class="count group-count">0</span>
          </div>
          <div v-if="activeManageGroups">
            <div v-for="item in manageGroups" :key="item.groupId" class="groupData">
              <img class="avatar" src="../assets/icon.png" alt="暂无头像" />
              <span class="groupName">{{ item.groupName }}</span>
            </div>
          </div>
          <div class="group" @click="activeJoinGroups = !activeJoinGroups">
            <svg class="icon youjiantou" aria-hidden="true">
              <use xlink:href="#icon-youjiantou"></use>
            </svg>
            <span>我加入的群聊</span>
            <span class="count group-count">0</span>
          </div>
          <div v-if="activeJoinGroups">
            <div v-for="item in joinGroups" :key="item.groupId" class="groupData">
              <img class="avatar" src="../assets/icon.png" alt="暂无头像" />
              <span class="groupName">{{ item.groupName }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="right">
        <FriendInfo v-if="selectedFriendId" :userId="selectedFriendId" :groupName="selectedFriendGroup"
          @sendMessage="handleSendMessage" @audioCall="handleAudioCall" @videoCall="handleVideoCall"
          @groupChanged="handleGroupChanged" />
        <div v-else class="no-selection">
          <div class="placeholder">
            <svg class="icon" aria-hidden="true">
              <use xlink:href="#icon-user"></use>
            </svg>
            <p>选择一个好友查看详细信息</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.contact {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
  /* 明确设置宽度 */
  overflow: hidden;
  /* 防止整个页面溢出 */
}

.drag {
  width: calc(100% - 150px);
  height: 30px;
  background-color: #fff;
  -webkit-app-region: drag;
}

.container {
  display: flex;
  flex: 1;
  height: calc(100vh - 30px);
  width: 100%;
  overflow: hidden;
  /* 防止容器溢出 */
}

.left {
  width: 280px;
  height: 100%;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  border-right: 1px solid #eee;
}

.right {
  flex: 1;
  /* 使用flex自动计算剩余宽度 */
  width: calc(100vw - 340px);
  max-width: calc(100vw - 340px);
  /* 防止超出 */
  height: 100%;
  background: #ffffff;
  overflow: hidden;
  min-width: 0;
  /* 允许flex项目收缩到内容以下 */
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.friends {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  margin-top: 10px;
  box-sizing: border-box;
  overflow: hidden;
  max-width: 100%;

  .friend {
    width: 100%;
    display: flex;
    align-items: center;
    padding: 8px 16px;
    cursor: pointer;
    border-radius: 8px;
    margin-bottom: 5px;
    transition: all 0.3s ease;
    text-overflow: ellipsis;
    white-space: nowrap;
    box-sizing: border-box;
    /* 确保padding包含在宽度内 */
    overflow: hidden;
    /* 防止内容溢出 */

    &:hover {
      background: #f5f5f5;
    }

    .avatar {
      width: 40px;
      height: 40px;
      object-fit: cover;
      border-radius: 50%;
      margin-right: 10px;
    }

    .info {
      display: flex;
      flex-direction: column;
      overflow: hidden;

      .remark {
        font-size: 15px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-bottom: 2px;
      }

      .status {
        font-size: 12px;
        color: #999;
        display: flex;
        align-items: center;
        gap: 2px;
        /* 减小元素间距 */

        .bracket {
          font-size: 12px;
          color: #999;
          line-height: 1;
          vertical-align: middle;
        }

        .icon {
          flex-shrink: 0;
          /* 禁止被压缩 */
          flex-grow: 0;
          /* 禁止被拉伸 */
          font-size: 12px;
          /* 设置图标大小与文字一致 */
          vertical-align: middle;
          /* 垂直居中对齐 */
        }

        .status-text {
          font-size: 12px;
          color: #999;
          line-height: 1;
          vertical-align: middle;
        }

        .signiture {
          font-size: 12px;
          color: #999;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
}

.title {
  padding: 10px;
  display: flex;
  align-items: center;
}

.notice {
  border-bottom: 1px solid #eee;
  padding: 10px;
}

.friendNotice {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  cursor: pointer;

  &:hover {
    background: #f5f5f5;
  }
}

.option {
  display: flex;
  position: relative;
  border-bottom: 1px solid #eee;

  div {
    flex: 1;
    text-align: center;
    padding: 10px;
    cursor: pointer;
    transition: all 0.3s ease;

    &.active {
      color: #409eff;
    }
  }
}

.slider {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 50%;
  height: 2px;
  background: #409eff;
  transition: transform 0.3s ease;
}

.friendGroup,
.unionGroup {
  flex: 1;
  overflow: hidden;
  /* 完全禁用滚动 */
  padding: 10px;

  .group {
    display: flex;
    align-items: center;
    padding: 8px;
    flex-wrap: wrap;
    width: 100%;
    cursor: pointer;

    .group-count {
      margin-left: auto;
      margin-right: 15px;
      /* 添加右边距，让数量往左移 */
      color: #999;
    }

    .youjiantou {
      margin-right: 10px;
    }

    .groupName {}

    .count {
      font-size: 10px;
    }
  }

  .groupData {
    display: flex;
    align-items: center;
    padding: 8px;
    flex-wrap: wrap;
    /* 启用换行 */
    width: 100%;
    /* 占满父容器宽度 */
    cursor: pointer;

    .avatar {
      width: 40px;
      height: 40px;
      object-fit: cover;
      border-radius: 50%;
      margin-right: 10px;
    }
  }
}

.add-button {
  position: relative;

  .jiajia {
    margin-left: 10px;
    font-size: 20px;
    color: #999;
    cursor: pointer;

    &:hover {
      color: #666;
    }
  }
}

.add-options {
  position: absolute;
  top: 100%;
  right: 0;
  width: 120px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  padding: 4px 0;

  .option-item {
    padding: 8px 16px;
    display: flex;
    align-items: center;
    cursor: pointer;

    &:hover {
      background: #f5f5f5;
    }

    i {
      margin-right: 8px;
      font-size: 16px;
      color: #666;
    }

    span {
      font-size: 13px;
      color: #333;
    }
  }
}

.friendNotice {
  cursor: pointer;
}



.no-selection {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.placeholder {
  text-align: center;
  color: #999;
}

.placeholder .icon {
  font-size: 48px;
  color: #ddd;
  margin-bottom: 16px;
}

.placeholder p {
  font-size: 14px;
  margin: 0;
}
</style>
