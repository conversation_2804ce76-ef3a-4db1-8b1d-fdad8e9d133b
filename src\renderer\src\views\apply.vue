<template>
  <div class="apply-friend">
    <!-- 标题栏 -->
    <div class="header">
      <span class="title">申请好友</span>
      <button class="close-btn" @click="closeDialog">
        <svg class="icon" aria-hidden="true">
          <use xlink:href="#icon-fork"></use>
        </svg>
      </button>
    </div>

    <!-- 用户信息 -->
    <div class="user-info">
      <img :src="userInfo.avatar" :alt="userInfo.name" class="user-avatar" />
      <div class="user-details">
        <div class="user-name">{{ userInfo.name }}</div>
        <div class="user-id">{{ userInfo.account }}</div>
      </div>
    </div>

    <!-- 表单内容 -->
    <div class="form-content">
      <!-- 验证信息 -->
      <div class="form-group">
        <label class="form-label">填写验证信息</label>
        <textarea
          v-model="verifyMessage"
          class="form-textarea"
          placeholder="我是！"
          rows="3"
        ></textarea>
      </div>

      <!-- 备注 -->
      <div class="form-group">
        <label class="form-label">备注</label>
        <input v-model="remark" type="text" class="form-input" placeholder="Len" />
      </div>

      <!-- 分组 -->
      <div class="form-group">
        <label class="form-label">分组</label>
        <select v-model="selectedGroup" class="form-select">
          <option
            v-for="item in friendGroups"
            :key="item.groupFriendId"
            :value="item.groupFriendId"
          >
            {{ item.groupName }}
          </option>
        </select>
      </div>

      <!-- 好友权限 -->
      <div class="form-group">
        <label class="form-label">好友权限</label>

        <!-- 权限选项卡片 -->
        <div class="permission-cards">
          <div
            class="permission-card"
            :class="{ active: permission === 'chat-space' }"
            @click="permission = 'chat-space'"
          >
            <div class="card-icon">⭐</div>
            <div class="card-text">聊天、空间动态等</div>
          </div>

          <div
            class="permission-card"
            :class="{ active: permission === 'chat-only' }"
            @click="permission = 'chat-only'"
          >
            <div class="card-icon">💬</div>
            <div class="card-text">仅聊天</div>
          </div>
        </div>

        <!-- 空间动态设置 -->
        <div class="space-settings">
          <div class="space-label">空间动态</div>

          <div class="space-option">
            <div class="space-info">
              <span class="space-icon">🔒</span>
              <span class="space-text">不让他/她看</span>
            </div>
            <label class="toggle-switch">
              <input v-model="hideFromThem" type="checkbox" />
              <span class="toggle-slider"></span>
            </label>
          </div>

          <div class="space-option">
            <div class="space-info">
              <span class="space-icon">👁️</span>
              <span class="space-text">不看他/她的</span>
            </div>
            <label class="toggle-switch">
              <input v-model="dontSeeTheirs" type="checkbox" />
              <span class="toggle-slider"></span>
            </label>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="footer">
      <button class="cancel-btn" @click="closeDialog">取消</button>
      <button class="send-btn" @click="sendRequest">发送</button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { getFriendGroups, addUser } from '../api/user'
import { ElMessage } from 'element-plus'

// 对方用户信息（从主进程接收）
const userInfo = ref({})

// 监听来自主进程的用户信息
const handleSetUserInfo = (event, userData) => {
  userInfo.value = {
    name: userData.nickname,
    userId: userData.userId,
    account: userData.account,
    avatar: userData.avatar
  }
}

// 好友分组信息
const friendGroups = ref([])
const getFriendGroupsGet = async () => {
  const data = await getFriendGroups()
  friendGroups.value = data.data
  // 数据加载完成后设置默认选中的分组
  if (friendGroups.value.length > 0) {
    selectedGroup.value = friendGroups.value[0].groupFriendId
  }
}

onMounted(() => {
  // 监听主进程发送的用户信息
  window.electron.ipcRenderer.on('set-user-info', handleSetUserInfo)

  // 主动请求用户信息（备用方案）
  setTimeout(() => {
    window.electron.ipcRenderer.send('request-user-info')
  }, 50)

  getFriendGroupsGet()
})

onUnmounted(() => {
  // 清理事件监听
  window.electron.ipcRenderer.removeListener('set-user-info', handleSetUserInfo)
})

// 表单数据
const verifyMessage = ref('我是！')
const remark = ref('Len')
const selectedGroup = ref()
const permission = ref('chat-space')
const hideFromThem = ref(false)
const dontSeeTheirs = ref(false)

// 方法
const closeDialog = () => {
  // 通知主进程关闭申请好友窗口
  window.electron.ipcRenderer.send('close-apply-window')
}

const sendRequest = async () => {
  const requestData = {
    oppositeId: userInfo.value.userId,
    leaveWord: verifyMessage.value,
    remark: remark.value,
    groupFriendId: selectedGroup.value
    /*     permission: permission.value,
    hideFromThem: hideFromThem.value,
    dontSeeTheirs: dontSeeTheirs.value */
  }
  const { data } = await addUser(requestData)
  if (data) {
    ElMessage({
      showClose: true,
      message: '申请成功',
      type: 'success'
    })
    // 关闭窗口
    closeDialog()
  } else {
    ElMessage({
      showClose: true,
      message: '申请失败',
      type: 'error'
    })
  }
}
</script>

<style lang="scss" scoped>
.apply-friend {
  width: 100%;
  height: 100vh;
  background: #f2f2f2;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .header {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 12px 16px;
    background: transparent;
    flex-shrink: 0;
    position: relative;
    -webkit-app-region: drag;
    /* 使标题栏可拖拽 */

    .title {
      font-size: 14px;
      font-weight: 500;
      color: #333;
      text-align: center;
    }

    .close-btn {
      background: none;
      border: none;
      font-size: 18px;
      color: #999;
      cursor: pointer;
      padding: 0;
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      position: absolute;
      right: 16px;
      -webkit-app-region: no-drag;
      /* 关闭按钮不可拖拽 */

      &:hover {
        color: #666;
      }
    }
  }

  .user-info {
    display: flex;
    align-items: center;
    padding: 16px;
    flex-shrink: 0;
    background: #f2f2f2;
    margin-left: 5px;

    .user-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      margin-right: 10px;
      object-fit: cover;
    }

    .user-details {
      .user-name {
        font-size: 14px;
        font-weight: 500;
        color: #333;
        margin-bottom: 2px;
      }

      .user-id {
        font-size: 12px;
        color: #999;
      }
    }
  }

  .form-content {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
    background: #f2f2f2;

    /* 自定义滚动条样式 */
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }

    &::-webkit-scrollbar-thumb:active {
      background: #909090;
    }
  }

  .form-group {
    margin-bottom: 16px;

    .form-label {
      display: block;
      font-size: 13px;
      color: #a5a5a5;
      margin-bottom: 6px;
      margin-left: 15px;
    }

    .form-textarea {
      width: 100%;
      padding: 8px 10px;
      border: 1px solid #fff;
      border-radius: 4px;
      font-size: 13px;
      color: #333;
      resize: none;
      /* 禁用调整大小功能，隐藏右下角图标 */
      min-height: 60px;
      font-family: inherit;
      box-sizing: border-box;

      &:focus {
        outline: none;
        border-color: #1890ff;
      }

      &::placeholder {
        color: #999;
      }
    }

    .form-input {
      width: 100%;
      padding: 8px 10px;
      border: 1px solid #fff;
      border-radius: 4px;
      font-size: 13px;
      color: #333;
      box-sizing: border-box;

      &:focus {
        outline: none;
        border-color: #1890ff;
      }

      &::placeholder {
        color: #999;
      }
    }

    .form-select {
      width: 100%;
      padding: 8px 10px;
      border: 1px solid #fff;
      border-radius: 4px;
      font-size: 13px;
      color: #333;
      background: #fff;
      cursor: pointer;
      box-sizing: border-box;

      &:focus {
        outline: none;
        border-color: #1890ff;
      }
    }
  }

  .permission-cards {
    margin-bottom: 16px;

    .permission-card {
      display: flex;
      align-items: center;
      padding: 12px;
      margin-bottom: 8px;
      background: #fff;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        background: #f0f8ff;
      }

      &.active {
        background: #e6f7ff;
      }

      .card-icon {
        font-size: 16px;
        margin-right: 10px;
      }

      .card-text {
        flex: 1;
        font-size: 13px;
        color: #333;
      }
    }
  }

  .space-settings {
    .space-label {
      font-size: 13px;
      color: #666;
      margin-bottom: 12px;
      margin-left: 15px;
      /* 与好友权限垂直对齐 */
    }

    .space-option {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px;
      margin-bottom: 0;
      /* 去掉选项间距 */
      background: #fff;
      border: 1px solid #fff;
      /* 边框改为白色 */
      border-radius: 6px;

      .space-info {
        display: flex;
        align-items: center;
        flex: 1;

        .space-icon {
          margin-right: 8px;
          font-size: 14px;
        }

        .space-text {
          font-size: 13px;
          color: #333;
        }
      }

      .toggle-switch {
        position: relative;
        display: inline-block;
        width: 36px;
        height: 20px;

        input {
          opacity: 0;
          width: 0;
          height: 0;
        }

        .toggle-slider {
          position: absolute;
          cursor: pointer;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: #ccc;
          transition: 0.3s;
          border-radius: 20px;

          &:before {
            position: absolute;
            content: '';
            height: 14px;
            width: 14px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: 0.3s;
            border-radius: 50%;
          }
        }

        input:checked + .toggle-slider {
          background-color: #1890ff;
        }

        input:checked + .toggle-slider:before {
          transform: translateX(16px);
        }
      }
    }
  }

  .footer {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    padding: 12px 16px;
    border-top: 1px solid #f0f0f0;
    background: #f2f2f2;
    flex-shrink: 0;

    .cancel-btn {
      padding: 6px 16px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      background: #fff;
      color: #666;
      font-size: 13px;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        border-color: #bbb;
        color: #333;
      }
    }

    .send-btn {
      padding: 6px 16px;
      border: none;
      border-radius: 4px;
      background: #1890ff;
      color: #fff;
      font-size: 13px;
      cursor: pointer;
      transition: all 0.2s;
      &:hover {
        background: #40a9ff;
      }
    }
  }
}
</style>
