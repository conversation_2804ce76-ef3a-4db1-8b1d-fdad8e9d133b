<script setup>
import { defineProps } from 'vue'

defineProps({
  icon: {
    type: String,
    required: true
  },
  name: {
    type: String,
    required: true
  },
  size: {
    type: String,
    default: ''
  },
  time: {
    type: String,
    required: true
  },
  source: {
    type: String,
    required: true
  }
})
</script>

<template>
  <div class="show-item">
    <div class="left-content">
      <div class="item-icon">
        <svg class="icon outerColor" aria-hidden="true">
          <use :xlink:href="`#${icon}`"></use>
        </svg>
      </div>
      <div class="item-info">
        <div class="item-name">{{ name }}</div>
        <div class="item-size" v-if="size">{{ size }}</div>
      </div>
    </div>
    <div class="right-content">
      <div class="item-time">{{ time }}</div>
      <div class="item-source">来自：{{ source }}</div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.show-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 12px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

.left-content {
  display: flex;
  align-items: center;
  
  .item-icon {
    margin-right: 16px;
    
    .icon {
      font-size: 32px;
      color: #1296db;
    }
  }
  
  .item-info {
    .item-name {
      font-size: 15px;
      color: #333;
      margin-bottom: 4px;
      font-weight: 500;
    }
    
    .item-size {
      font-size: 13px;
      color: #8a8a8a;
    }
  }
}

.right-content {
  text-align: right;
  
  .item-time {
    font-size: 13px;
    color: #8a8a8a;
    margin-bottom: 4px;
  }
  
  .item-source {
    font-size: 13px;
    color: #1296db;
  }
}
</style>