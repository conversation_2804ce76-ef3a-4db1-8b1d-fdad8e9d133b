import { defineStore } from 'pinia'
import { useWebSocket } from '../utils/useWebSocket'

export const useChatStore = defineStore('chat', {
  state: () => ({
    userInfo: null, // 用户信息
    wsSendMessage: null, // websocket发送消息的方法
    socket: null, // websocket连接
    chatSession: [], // 聊天会话列表
    messages: [], // [{"chatSessionId": chatSessionId,"groupMessageChatVO" :groupMessageChatVO（群聊） / messages(私聊)}]
    activeChatSessionId: null, // 当前激活的聊天会话id
    noticeType: {
      join: 3,
      quit: 1,
      setAdmin: 2,
      kick: 4
    }
  }),
  actions: {
    async initWebSocket(sid) {
      const {
        sendMessage: wsSend,
        socket: wsSocket,
        connectionPromise
      } = useWebSocket(`ws://localhost:8101/api/websocket/chat/${sid}`)
      this.wsSendMessage = wsSend
      this.socket = wsSocket

      // 等待WebSocket连接建立
      try {
        await connectionPromise
        console.log('WebSocket连接已成功建立')
        return true
      } catch (error) {
        console.error('WebSocket连接失败:', error)
        throw error
      }
    },
    sendMessage(message) {
      if (this.wsSendMessage) {
        this.wsSendMessage(message)
      }
    },
    getSocket() {
      return this.socket
    },
    // 设置所有会话列表
    setChatSession(chatSession) {
      this.chatSession = chatSession
    },
    // 设置单个会话
    setChatSessionOne(chatSession, nickname) {
      this.chatSession = [
        ...this.chatSession,
        {
          ...chatSession,
          nickname: nickname
        }
      ]
    },
    // 通过chatSession, message更新单个会话
    updateChatSessionOne(chatSession, message) {
      const index = this.chatSession.findIndex(
        (item) => Number(item.chatSessionId) === Number(chatSession.chatSessionId)
      )
      const oldChatSession = this.chatSession.find(
        (item) => Number(item.chatSessionId) === Number(chatSession.chatSessionId)
      )
      this.chatSession[index] = {
        avatar: oldChatSession.avatar,
        chatSessionId: oldChatSession.chatSessionId,
        messageName: oldChatSession.messageName,
        latestMessageTime: chatSession.latestMessageTime,
        content: message.content,
        nickname: message?.nickname,
        participantType: oldChatSession.participantType,
        participant: oldChatSession.participant,
        unreadCount:
          oldChatSession.chatSessionId === this.activeChatSessionId
            ? 0
            : oldChatSession.unreadCount + 1,
        userId: oldChatSession.userId
      }
    },
    // 发送消息时，更新自己的单条会话信息
    updateSendMsgChatSession(message) {
      const csTemp = this.chatSession.find(
        (item) => Number(item.chatSessionId) === Number(this.activeChatSessionId)
      )
      this.chatSession[
        this.chatSession.findIndex(
          (item) => Number(item.chatSessionId) === Number(this.activeChatSessionId)
        )
      ] = {
        chatSessionId: csTemp.chatSessionId,
        avatar: csTemp.avatar,
        messageName: csTemp.messageName,
        latestMessageTime: new Date(),
        content: message.content,
        nickname: csTemp.nickname,
        participantType: csTemp.participantType,
        participant: csTemp.participant,
        unreadCount: 0,
        userId: csTemp.userId
      }
    },
    getChatSession(chatSessionId) {
      const result = this.chatSession.find((item) => {
        const strictMatch = item.chatSessionId === chatSessionId
        const looseMatch = item.chatSessionId == chatSessionId
        const stringMatch = String(item.chatSessionId) === String(chatSessionId)
        const numberMatch = Number(item.chatSessionId) === Number(chatSessionId)
        return strictMatch || looseMatch || stringMatch || numberMatch
      })
      return result
    },
    // 判断会话是否存在
    existChatSession(chatSession) {
      return this.chatSession.some(
        (item) => Number(item.chatSessionId) === Number(chatSession.chatSessionId)
      )
    },
    getMessages(chatSessionId) {
      if (chatSessionId) {
        // 如果传入了 chatSessionId，返回对应的消息
        const messageItem = this.messages.find(
          (item) => Number(item.chatSessionId) === Number(chatSessionId)
        )
        return messageItem ? messageItem.groupMessageChatVO : []
      } else {
        // 如果没有传入 chatSessionId，返回所有消息
        return this.messages
      }
    },
    // 判断消息是否存在
    existMessages(chatSessionId) {
      return this.messages.some((item) => Number(item.chatSessionId) === Number(chatSessionId))
    },
    // 设置所有消息
    setMessageAll(chatSessionId, data) {
      this.messages = [...this.messages, { chatSessionId, groupMessageChatVO: data }]
    },
    // 设置私聊消息
    setMessages(chatSessionId, message) {
      this.messages.findIndex((item) => Number(item.chatSessionId) === Number(chatSessionId)) === -1
        ? this.messages.push({ chatSessionId, messages: [message] })
        : this.messages[
            this.messages.findIndex((item) => Number(item.chatSessionId) === Number(chatSessionId))
          ].groupMessageChatVO.push(message)
    },
    // 设置群聊消息
    setMessagesVO(chatSessionId, messageVO) {
      this.messages.findIndex((item) => Number(item.chatSessionId) === Number(chatSessionId)) === -1
        ? this.messages.push({ chatSessionId, groupMessageChatVO: messageVO })
        : this.messages[
            this.messages.findIndex((item) => Number(item.chatSessionId) === Number(chatSessionId))
          ].groupMessageChatVO.messagesVOList.push(messageVO)
    },
    setUserInfo(userInfo) {
      this.userInfo = userInfo
    },
    getUserInfo() {
      return this.userInfo
    },
    setActiveChatSessionId(chatSessionId) {
      this.activeChatSessionId = chatSessionId
    },
    getActiveChatSessionId() {
      return this.activeChatSessionId
    }
  }
})
