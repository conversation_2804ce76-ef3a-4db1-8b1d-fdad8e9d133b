<script setup></script>

<template>
  <div class="notice">
    <div class="notice-item">
      <div class="left-content">
        <div class="photo">
          <slot name="photo"></slot>
        </div>
        <div class="content">
          <div class="friend-info">
            <slot name="friend-name"></slot>
          </div>
          <div class="message">
            <slot name="message"></slot>
          </div>
        </div>
      </div>
      <div class="action">
        <slot name="action"></slot>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.notice {
  padding: 8px 0;
}

.notice-item {
  display: flex;
  padding: 12px 16px;
  background: #fff;
  border-bottom: 1px solid #eee;
  border-radius: 8px;
  justify-content: space-between;
  align-items: center;
  margin: 0 8px 8px 8px;

  &:last-child {
    border-bottom: none;
  }
}

.left-content {
  display: flex;
  align-items: center;
}

.photo {
  width: 40px;
  height: 40px;
  margin-right: 12px;
  border-radius: 50%;
  overflow: hidden;
}

.content {
  display: flex;
  flex-direction: column;
}

.friend-info {
  display: flex;
  margin-bottom: 4px;
}

.message {
  margin-bottom: 8px;
}

.action {
  display: flex;
  align-items: center;
}
</style>
