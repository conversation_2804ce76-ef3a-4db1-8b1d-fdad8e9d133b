<template>
  <div id="userinfo">
    <div class="profile-card">
      <div class="profile-header">
        <div class="left-info">
          <img :src="userInfo?.avatar" alt="" />
          <div class="user-info">
            <div class="name">{{ userInfo?.nickname }}</div>
            <div class="qq">QQ {{ userInfo?.account }}</div>
            <div class="status-container" @click="handleStatusClick">
              <svg class="icon" aria-hidden="true">
                <use :xlink:href="`#${currentStatusIcon}`"></use>
              </svg>
              <div class="status">{{ userInfo?.status }}</div>
            </div>
          </div>
        </div>
        <div class="like-info">
          <svg class="icon" aria-hidden="true">
            <use :xlink:href="`#icon-dianzan`"></use>
          </svg>
          <span>{{ userInfo.likeCount }}</span>
        </div>
      </div>
      <div class="profile-content">
        <div class="info-row has-icons">
          <div class="label">等级</div>
          <div class="icons level-tooltip-container" @mouseenter="showTooltip = true" @mouseleave="showTooltip = false">
            <svg v-for="(icon, index) in levelIcons" :key="index" class="icon level-icon" aria-hidden="true">
              <use :xlink:href="`#${icon}`"></use>
            </svg>
            <div v-if="showTooltip" class="level-tooltip">
              当前等级：{{ userInfo?.grade || 0 }}
            </div>
          </div>
        </div>
        <div class="info-row has-icons">
          <div class="label">特权</div>
          <div class="privileges">
            <div v-for="(privilege, index) in privileges" :key="index" class="privilege-container"
              @mouseenter="showPrivilegeTooltip = privilege.isActive ? index : null"
              @mouseleave="showPrivilegeTooltip = null">
              <svg class="icon privilege-icon"
                :class="{ 'privilege-active': privilege.isActive, 'privilege-inactive': !privilege.isActive }"
                aria-hidden="true">
                <use :xlink:href="`#${privilege.icon}`"></use>
              </svg>
              <div v-if="showPrivilegeTooltip === index && privilege.isActive" class="privilege-tooltip">
                {{ privilege.level }}级
              </div>
            </div>
          </div>
        </div>
        <div class="info-row">
          <div class="label">签名</div>
          <div class="content">
            <span>{{ userInfo?.signiture }}</span>
          </div>
        </div>
        <div class="info-row">
          <div class="label">所在地</div>
          <div class="content">
            <span>{{ userInfo?.country }} - {{ userInfo?.province }}</span>
          </div>
        </div>
        <div class="info-row">
          <div class="label">QQ空间</div>
          <div class="content qzone-link" @click="handleQzoneClick">
            <span>查看我的QQ空间</span>
            <div class="arrow-icon">
              <svg class="icon" aria-hidden="true">
                <use :xlink:href="`#icon-right-arrow`"></use>
              </svg>
            </div>
          </div>
        </div>
        <div class="profile-footer">
          <button class="edit-btn" @click="handleEditProfile">编辑资料</button>
          <button class="send-msg-btn" @click="handleSendMessage">发消息</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, onMounted, onUnmounted } from "vue";
import { useChatStore } from "../../store/chatStore";
import { statusList } from "../../utils/status";
import { calculateLevelIcons } from "../../utils/global";

const chatStore = useChatStore();

// 使用 computed 让 userInfo 响应式地监听 store 变化
const userInfo = computed(() => chatStore.getUserInfo());

// 控制tooltip显示
const showTooltip = ref(false);

// 控制特权tooltip显示
const showPrivilegeTooltip = ref(null);

// 计算当前状态图标
const currentStatusIcon = computed(() => {
  const userStatus = userInfo.value?.status || '听歌中';
  const statusItem = statusList.find(item => item.text === userStatus);
  return statusItem ? statusItem.icon : 'icon-listening';
});

// 动态计算等级图标
const levelIcons = computed(() => {
  const grade = userInfo.value?.grade || 0;
  return calculateLevelIcons(grade);
});

// 所有可能的特权图标及其对应的后端字符串
const allPrivileges = [
  { icon: "icon-pokeball", key: "pokeball1" },
  { icon: "icon-ultra-ball", key: "pokeball2" },
  { icon: "icon-dashiqiu", key: "pokeball3" },
  { icon: "icon-insignia-", key: "pokeball4" },
  { icon: "icon-a-PokeTrainerTwoStar", key: "pokeball5" },
  { icon: "icon-egg", key: "pokeball6" },
  { icon: "icon-emoji_u1f423", key: "pokeball7" },
  { icon: "icon-pokecoin", key: "pokeball8" },
  { icon: "icon-shenqibaobeixiaohuolong---shuiyin", key: "pokeball9" },
  { icon: "icon-autocode", key: "pokeball10" }
];

// 动态计算特权图标状态
const privileges = computed(() => {
  const userVips = userInfo.value?.vips || [];
  return allPrivileges.map((privilege) => {
    // 查找匹配的vip信息（格式：pokeball1-1）
    const vipInfo = userVips.find(vip => vip.startsWith(privilege.key + '-'));
    const isActive = !!vipInfo;
    const level = isActive ? vipInfo.split('-')[1] : null;

    return {
      icon: privilege.icon,
      isActive,
      level // 特权等级
    };
  });
});

const handleStatusClick = () => {
  window.electron.ipcRenderer.send("open-status-window");
};

const handleQzoneClick = () => {
  window.electron.ipcRenderer.send("open-qzone-window");
};

const handleSendMessage = () => {
  window.electron.ipcRenderer.send("send-message", userInfo.value.account);
};

const handleEditProfile = () => {
  window.electron.ipcRenderer.send("edit-profile");
};

// 监听主进程发送的用户状态更新
const handleUserStatusUpdate = (event, userData) => {
  if (userData) {
    chatStore.setUserInfo(userData);
  }
};

// 组件挂载时添加监听器
onMounted(() => {
  window.electron.ipcRenderer.on('user-status-updated', handleUserStatusUpdate);
});

// 组件卸载时移除监听器
onUnmounted(() => {
  window.electron.ipcRenderer.removeListener('user-status-updated', handleUserStatusUpdate);
});
</script>

<style scoped>
#userinfo {
  .profile-card {
    position: absolute;
    left: 70px;
    top: 70px;
    width: 320px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12), 0 2px 8px rgba(0, 0, 0, 0.08);
    z-index: 1000;
    border: 1px solid rgba(0, 0, 0, 0.06);

    .profile-header {
      padding: 16px;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      border-bottom: 1px solid #f0f0f0;
      background: linear-gradient(135deg, #fafafa 0%, #ffffff 100%);

      .left-info {
        display: flex;
        gap: 12px;

        img {
          width: 56px;
          height: 56px;
          border-radius: 50%;
          margin: 0;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          border: 2px solid #ffffff;
          object-fit: cover;
        }
      }

      .like-info {
        display: flex;
        flex-direction: column;
        align-items: center;
        align-self: center;
        margin-right: 8px;

        .icon {
          font-size: 16px;
          color: #666;
          margin: 0;
          padding: 0;

          &:hover {
            color: #409eff;
          }
        }

        span {
          font-size: 12px;
          color: #666;
          margin-top: 2px;
        }
      }

      .user-info {
        .name {
          font-size: 16px;
          font-weight: 600;
          color: #1a1a1a;
          margin-bottom: 6px;
          line-height: 1.2;
        }

        .qq {
          font-size: 13px;
          color: #666;
          margin-bottom: 8px;
          font-weight: 400;
        }

        .status-container {
          display: flex;
          align-items: center;
          gap: 4px;
          padding: 2px 4px;
          border-radius: 2px;
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            background-color: rgba(0, 0, 0, 0.05);
          }

          &:active {
            background-color: rgba(0, 0, 0, 0.1);
            box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
          }

          .icon {
            font-size: 13px;
            color: #ffd719;
            margin: 0;
            padding: 0;
          }

          .status {
            font-size: 13px;
            color: #999;
            margin: 0;
          }
        }
      }
    }

    .profile-content {
      padding: 12px 16px;

      .info-row {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        padding: 6px 0;
        border-radius: 6px;
        transition: background-color 0.2s ease;

        &:hover {
          background-color: rgba(0, 0, 0, 0.02);
        }

        /* 对于包含图标的行，使用顶部对齐 */
        &.has-icons {
          align-items: flex-start;

          .label {
            padding-top: 2px;
            /* 微调标签位置以与第一行图标对齐 */
          }
        }

        .label {
          width: 52px;
          font-size: 13px;
          color: #666;
          flex-shrink: 0;
          font-weight: 500;
        }

        .content {
          font-size: 13px;
          color: #666;
          display: flex;
          align-items: center;
          flex: 1;

          span {
            color: #1a1a1a;
            font-weight: 400;
          }
        }

        .icons {
          display: flex;
          flex-wrap: wrap;
          gap: 2px;
          justify-content: flex-start;
          align-content: flex-start;
          flex: 1;
        }

        .level-tooltip-container {
          position: relative;
          cursor: pointer;

          span {
            font-size: 14px;
          }

          .level-icon {
            font-size: 24px;
            color: #ffd700;
            margin: 0 2px;
            padding: 0;
            transition: all 0.2s ease;

            &:hover {
              transform: scale(1.1);
            }
          }
        }

        .level-tooltip {
          position: absolute;
          top: -35px;
          left: 50%;
          transform: translateX(-50%);
          background: rgba(0, 0, 0, 0.8);
          color: white;
          padding: 6px 10px;
          border-radius: 4px;
          font-size: 12px;
          white-space: nowrap;
          z-index: 1000;
          pointer-events: none;

          &::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            border: 4px solid transparent;
            border-top-color: rgba(0, 0, 0, 0.8);
          }
        }

        .privileges {
          display: flex;
          flex-wrap: wrap;
          gap: 6px;
          justify-content: flex-start;
          align-content: flex-start;
          flex: 1;
          max-width: 160px;
          /* 调整宽度以控制每行5个图标 */

          span {
            font-size: 14px;
          }

          .privilege-container {
            position: relative;
            display: inline-block;
          }

          .privilege-icon {
            font-size: 22px;
            /* 调大图标尺寸 */
            margin: 0;
            padding: 0;
            transition: all 0.2s ease;
            flex: 0 0 auto;
            /* 防止图标被压缩 */

            &:hover {
              transform: scale(1.1);
            }

            /* 未激活状态 - 暗色 */
            &.privilege-inactive {
              color: #cccccc;
              opacity: 0.4;
            }

            /* 激活状态 - 亮色 */
            &.privilege-active {
              color: #ff6b6b;
              opacity: 1;
            }
          }

          .privilege-tooltip {
            position: absolute;
            top: -35px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            z-index: 1000;
            pointer-events: none;

            &::after {
              content: '';
              position: absolute;
              top: 100%;
              left: 50%;
              transform: translateX(-50%);
              border: 4px solid transparent;
              border-top-color: rgba(0, 0, 0, 0.8);
            }
          }
        }
      }
    }

    .profile-footer {
      border-top: 1px solid #f0f0f0;
      margin-top: 6px;
      padding: 12px 16px;
      display: flex;
      justify-content: space-between;
      gap: 12px;
      background: #fafafa;

      .edit-btn,
      .send-msg-btn {
        flex: 1;
        padding: 10px 0;
        font-size: 13px;
        border-radius: 6px;
        cursor: pointer;
        text-align: center;
        outline: none;
        font-weight: 500;
        transition: all 0.2s ease;
      }

      .edit-btn {
        color: #555;
        background-color: #ffffff;
        border: 1px solid #e0e0e0;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

        &:hover {
          background: #f8f8f8;
          color: #409eff;
          border-color: #409eff;
          transform: translateY(-1px);
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
        }
      }

      .send-msg-btn {
        color: #fff;
        background: linear-gradient(135deg, #12b7f5 0%, #0e9fd9 100%);
        border: 1px solid #12b7f5;
        box-shadow: 0 2px 6px rgba(18, 183, 245, 0.3);

        &:hover {
          background: linear-gradient(135deg, #0e9fd9 0%, #0a8bc2 100%);
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(18, 183, 245, 0.4);
        }
      }
    }

    .qzone-link {
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      width: 100%;
      padding: 4px 6px;
      border-radius: 4px;
      transition: all 0.2s ease;

      &:hover {
        background-color: rgba(64, 158, 255, 0.08);

        span {
          color: #409eff;
        }

        .icon {
          color: #409eff;
          transform: translateX(2px);
        }
      }

      span {
        color: #1a1a1a;
        font-weight: 400;
      }

      .arrow-icon {
        display: flex;
        align-items: center;
      }

      .icon {
        font-size: 12px;
        color: #999;
        transition: all 0.2s ease;
      }
    }
  }
}
</style>
