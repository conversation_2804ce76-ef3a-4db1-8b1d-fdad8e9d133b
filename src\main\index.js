import { app, shell, BrowserWindow, ipcMain } from 'electron'
import { join } from 'path'
import { electronApp, optimizer, is } from '@electron-toolkit/utils'
import icon from '../../resources/icon.png?asset'
import { ipcLogin, ipcNetSearch } from './ipc'

let width = 330
let height = 450
let statusWindow = null
let netSearchWindow = null
let applyWindow = null
let joinGroupWindow = null
let profileWindow = null
let currentUserData = null // 存储当前要申请的用户信息
let currentGroupData = null // 存储当前要申请的群组信息
let currentProfileData = null // 存储当前用户资料信息

function createWindow() {
  // Create the browser window.
  const mainWindow = new BrowserWindow({
    width: width,
    height: height,
    show: false,
    autoHideMenuBar: true,
    titleBarStyle: 'hidden',
    frame: false,
    resizable: false,
    // 不允许最大化
    maximizable: false,
    transparent: true,
    ...(process.platform === 'linux' ? { icon } : {}),
    webPreferences: {
      preload: join(__dirname, '../preload/index.js'),
      sandbox: false
    }
  })

  mainWindow.on('ready-to-show', () => {
    mainWindow.show()
  })

  mainWindow.webContents.setWindowOpenHandler((details) => {
    shell.openExternal(details.url)
    return { action: 'deny' }
  })

  // HMR for renderer base on electron-vite cli.
  // Load the remote URL for development or the local html file for production.
  if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
    mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL'])
  } else {
    mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
  }

  // 监听事件
  ipcLogin((event) => {
    // 允许窗口改变大小
    mainWindow.setResizable(true)
    // 允许最大化
    mainWindow.setMaximizable(true)
    // 重新设置宽高
    mainWindow.setSize(1140, 700)
  })

  ipcNetSearch((event) => {
    createNetSearchWindow()
  })

  ipcMain.on('close-window', () => {
    // 检查当前焦点窗口，决定关闭哪个窗口
    const focusedWindow = BrowserWindow.getFocusedWindow()
    if (focusedWindow === netSearchWindow) {
      netSearchWindow.close()
      netSearchWindow = null
    } else {
      mainWindow.close()
    }
  })

  ipcMain.on('minimize-window', () => {
    // 检查当前焦点窗口，决定最小化哪个窗口
    const focusedWindow = BrowserWindow.getFocusedWindow()
    if (focusedWindow === netSearchWindow) {
      netSearchWindow.minimize()
    } else {
      mainWindow.minimize()
    }
  })

  ipcMain.on('maximize-window', () => {
    // 检查当前焦点窗口，决定最大化哪个窗口
    const focusedWindow = BrowserWindow.getFocusedWindow()
    if (focusedWindow === netSearchWindow) {
      if (netSearchWindow.isMaximized()) {
        netSearchWindow.restore()
      } else {
        netSearchWindow.maximize()
      }
    } else {
      if (mainWindow.isMaximized()) {
        mainWindow.restore()
      } else {
        mainWindow.maximize()
      }
    }
  })

  ipcMain.on('add-user', (event, user) => {
    currentUserData = user // 存储用户信息
    createApplyWindow()
  })

  ipcMain.on('add-group', (event, group) => {
    currentGroupData = group // 存储群组信息
    createJoinGroupWindow()
  })

  // 处理申请页面请求用户信息
  ipcMain.on('request-user-info', (event) => {
    if (currentUserData && applyWindow) {
      applyWindow.webContents.send('set-user-info', currentUserData)
    }
  })

  // 处理关闭申请好友窗口
  ipcMain.on('close-apply-window', (event) => {
    if (applyWindow) {
      applyWindow.close()
    }
  })

  // 处理关闭申请加群窗口
  ipcMain.on('close-join-group-window', (event) => {
    if (joinGroupWindow) {
      joinGroupWindow.close()
    }
  })
}

// 用户状态窗口
function createStatusWindow() {
  statusWindow = new BrowserWindow({
    width: 320,
    height: 540,
    show: false,
    autoHideMenuBar: true,
    titleBarStyle: 'hidden',
    frame: false,
    resizable: false,
    maximizable: false,
    webPreferences: {
      preload: join(__dirname, '../preload/index.js'),
      sandbox: false
    }
  })

  if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
    statusWindow.loadURL(`${process.env['ELECTRON_RENDERER_URL']}/#/status`)
  } else {
    statusWindow.loadFile(join(__dirname, '../renderer/index.html'), {
      hash: 'status'
    })
  }

  statusWindow.once('ready-to-show', () => {
    statusWindow.show()
  })
}

// 全网搜索窗口
function createNetSearchWindow() {
  // 如果窗口已存在，直接显示
  if (netSearchWindow) {
    netSearchWindow.show()
    netSearchWindow.focus()
    return
  }

  netSearchWindow = new BrowserWindow({
    width: 800,
    height: 600,
    show: false, //创建时不立即显示（等内容加载完再显示）
    autoHideMenuBar: true, //自动隐藏菜单栏
    frame: false, //移除默认标题栏
    // 适用于 macOS（可选）
    titleBarStyle: 'hidden',
    resizable: true,
    maximizable: true,
    // 安全相关配置
    webPreferences: {
      preload: join(__dirname, '../preload/index.js'),
      sandbox: false,
      nodeIntegration: false,
      contextIsolation: true
    }
  })

  // 开发环境和生产环境的处理
  if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
    // 开发环境：加载开发服务器的URL
    netSearchWindow.loadURL(`${process.env['ELECTRON_RENDERER_URL']}/#/search`)
  } else {
    // 生产环境：加载打包后的本地HTML文件
    netSearchWindow.loadFile(join(__dirname, '../renderer/index.html'), {
      hash: 'search'
    })
  }

  // 只监听一次事件  等待页面内容加载完成后再显示窗口
  netSearchWindow.once('ready-to-show', () => {
    netSearchWindow.show()
  })

  // 窗口关闭时清理引用
  netSearchWindow.on('closed', () => {
    netSearchWindow = null
  })
}

// 申请好友窗口
function createApplyWindow() {
  // 如果窗口已存在，直接显示
  if (applyWindow) {
    applyWindow.show()
    applyWindow.focus()
    return
  }

  applyWindow = new BrowserWindow({
    width: 330,
    height: 490,
    show: false, //创建时不立即显示（等内容加载完再显示）
    autoHideMenuBar: true, //自动隐藏菜单栏
    frame: false, //移除默认标题栏
    // 适用于 macOS（可选）
    titleBarStyle: 'hidden',
    resizable: false,
    maximizable: false,
    // 安全相关配置
    webPreferences: {
      preload: join(__dirname, '../preload/index.js'),
      sandbox: false,
      nodeIntegration: false,
      contextIsolation: true
    }
  })

  // 开发环境和生产环境的处理
  if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
    // 开发环境：加载开发服务器的URL
    applyWindow.loadURL(`${process.env['ELECTRON_RENDERER_URL']}/#/apply`)
  } else {
    // 生产环境：加载打包后的本地HTML文件
    applyWindow.loadFile(join(__dirname, '../renderer/index.html'), {
      hash: 'apply'
    })
  }

  // 只监听一次事件  等待页面内容加载完成后再显示窗口
  applyWindow.once('ready-to-show', () => {
    applyWindow.show()
    // 延迟发送用户数据，确保Vue组件已经挂载
    if (currentUserData) {
      setTimeout(() => {
        applyWindow.webContents.send('set-user-info', currentUserData)
      }, 100)
    }
  })

  // 窗口关闭时清理引用
  applyWindow.on('closed', () => {
    applyWindow = null
    currentUserData = null // 清空当前用户数据
  })
}

// 申请加群窗口
function createJoinGroupWindow() {
  // 如果窗口已存在，直接显示
  if (joinGroupWindow) {
    joinGroupWindow.show()
    joinGroupWindow.focus()
    return
  }

  joinGroupWindow = new BrowserWindow({
    width: 330,
    height: 490,
    show: false, //创建时不立即显示（等内容加载完再显示）
    autoHideMenuBar: true, //自动隐藏菜单栏
    frame: false, //移除默认标题栏
    // 适用于 macOS（可选）
    titleBarStyle: 'hidden',
    resizable: false,
    maximizable: false,
    // 安全相关配置
    webPreferences: {
      preload: join(__dirname, '../preload/index.js'),
      sandbox: false,
      nodeIntegration: false,
      contextIsolation: true
    }
  })

  // 开发环境和生产环境的处理
  if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
    // 开发环境：加载开发服务器的URL
    joinGroupWindow.loadURL(`${process.env['ELECTRON_RENDERER_URL']}/#/joinGroup`)
  } else {
    // 生产环境：加载打包后的本地HTML文件
    joinGroupWindow.loadFile(join(__dirname, '../renderer/index.html'), {
      hash: 'joinGroup'
    })
  }

  // 只监听一次事件  等待页面内容加载完成后再显示窗口
  joinGroupWindow.once('ready-to-show', () => {
    joinGroupWindow.show()
    // 延迟发送群聊数据，确保Vue组件已经挂载
    if (currentGroupData) {
      setTimeout(() => {
        joinGroupWindow.webContents.send('set-group-info', currentGroupData)
      }, 100)
    }
  })

  // 窗口关闭时清理引用
  joinGroupWindow.on('closed', () => {
    joinGroupWindow = null
    currentGroupData = null // 清空当前群组数据
  })
}

// 编辑资料窗口
function createProfileWindow() {
  // 如果窗口已存在，直接显示
  if (profileWindow) {
    profileWindow.show()
    profileWindow.focus()
    return
  }

  profileWindow = new BrowserWindow({
    width: 500,
    height: 600,
    show: false, //创建时不立即显示（等内容加载完再显示）
    autoHideMenuBar: true, //自动隐藏菜单栏
    frame: false, //移除默认标题栏
    // 适用于 macOS（可选）
    titleBarStyle: 'hidden',
    resizable: false,
    maximizable: false,
    // 安全相关配置
    webPreferences: {
      preload: join(__dirname, '../preload/index.js'),
      sandbox: false,
      nodeIntegration: false,
      contextIsolation: true
    }
  })

  // 开发环境和生产环境的处理
  if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
    // 开发环境：加载开发服务器的URL
    profileWindow.loadURL(`${process.env['ELECTRON_RENDERER_URL']}/#/editProfile`)
  } else {
    // 生产环境：加载打包后的本地HTML文件
    profileWindow.loadFile(join(__dirname, '../renderer/index.html'), {
      hash: 'editProfile'
    })
  }

  // 只监听一次事件  等待页面内容加载完成后再显示窗口
  profileWindow.once('ready-to-show', () => {
    profileWindow.show()
    // 延迟发送用户资料数据，确保Vue组件已经挂载
    if (currentProfileData) {
      setTimeout(() => {
        profileWindow.webContents.send('set-profile-info', currentProfileData)
      }, 100)
    }
  })

  // 窗口关闭时清理引用
  profileWindow.on('closed', () => {
    profileWindow = null
    currentProfileData = null // 清空当前用户资料数据
  })
}

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.whenReady().then(() => {
  // Set app user model id for windows
  electronApp.setAppUserModelId('com.electron')

  // Default open or close DevTools by F12 in development
  // and ignore CommandOrControl + R in production.
  // see https://github.com/alex8088/electron-toolkit/tree/master/packages/utils
  app.on('browser-window-created', (_, window) => {
    optimizer.watchWindowShortcuts(window)
  })

  // IPC test
  ipcMain.on('ping', () => console.log('pong'))

  createWindow()

  app.on('activate', function () {
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (BrowserWindow.getAllWindows().length === 0) createWindow()
  })
})

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// In this file you can include the rest of your app"s specific main process
// code. You can also put them in separate files and require them here.

ipcMain.on('open-status-window', () => {
  if (!statusWindow) {
    createStatusWindow()
  }
  statusWindow.show()
})

ipcMain.on('close-status-window', () => {
  if (statusWindow) {
    statusWindow.close()
    statusWindow = null
  }
})

// 搜索窗口控制事件
ipcMain.on('close-search-window', () => {
  if (netSearchWindow) {
    netSearchWindow.close()
    netSearchWindow = null
  }
})

ipcMain.on('minimize-search-window', () => {
  if (netSearchWindow) {
    netSearchWindow.minimize()
  }
})

ipcMain.on('maximize-search-window', () => {
  if (netSearchWindow) {
    if (netSearchWindow.isMaximized()) {
      netSearchWindow.restore()
    } else {
      netSearchWindow.maximize()
    }
  }
})

// 编辑资料窗口事件处理
ipcMain.on('edit-profile', (event) => {
  // 获取当前用户信息
  const mainWindow = BrowserWindow.getFocusedWindow()
  if (mainWindow) {
    // 从主窗口获取用户信息
    const userInfo = mainWindow.webContents.executeJavaScript('window.localStorage.getItem("userInfo")')
      .then((result) => {
        if (result) {
          try {
            currentProfileData = JSON.parse(result)
            createProfileWindow()
          } catch (error) {
            console.error('解析用户信息失败:', error)
          }
        } else {
          createProfileWindow()
        }
      })
      .catch((error) => {
        console.error('获取用户信息失败:', error)
        createProfileWindow()
      })
  } else {
    createProfileWindow()
  }
})

ipcMain.on('request-profile-info', (event) => {
  if (currentProfileData && profileWindow) {
    profileWindow.webContents.send('set-profile-info', currentProfileData)
  }
})

ipcMain.on('save-profile', (event, profileData) => {
  // 保存用户资料
  currentProfileData = profileData
  
  // 更新主窗口的用户信息
  const mainWindow = BrowserWindow.getAllWindows().find(win => win !== profileWindow)
  if (mainWindow) {
    mainWindow.webContents.send('update-user-info', profileData)
  }
})

ipcMain.on('close-profile-window', () => {
  if (profileWindow) {
    profileWindow.close()
    profileWindow = null
  }
})

// 处理用户信息更新，通知所有窗口
ipcMain.on('user-info-updated', (event, userInfo) => {
  console.log('收到用户信息更新，广播到所有窗口:', userInfo)

  // 获取所有窗口
  const allWindows = BrowserWindow.getAllWindows()

  // 向所有窗口发送更新消息（除了发送者）
  allWindows.forEach(window => {
    if (window.webContents !== event.sender) {
      window.webContents.send('update-user-info', userInfo)
    }
  })
})
