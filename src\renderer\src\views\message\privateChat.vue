<script setup>
import { ref, watch } from 'vue'
import chatHeader from '../../components/chatHeader.vue'
import messageInputArea from '../../components/messageInputArea.vue'
import MessageList from '../../components/messageList.vue'
import { useChatStore } from '../../store/chatStore'
import { useRoute } from 'vue-router'
import { getChats } from '../../api/message'
import { getSendMessageInfo } from '../../utils/global'

const chatStore = useChatStore()
const route = useRoute()
// 将 chatSessionId 改为响应式
const chatSessionId = ref(Number(route.params.id))

const userInfo = ref(chatStore.getUserInfo())
const chatInfo = ref({
  name: ''
})
const messageList = ref([]) // 聊天消息列表

// 初始化聊天数据的函数
const initChatData = async (sessionId) => {
  try {
    // 先获取会话信息并设置名称
    const chatSession = chatStore.getChatSession(sessionId)
    if (chatSession) {
      // 优先使用 messageName，如果没有则使用 nickname
      chatInfo.value.name = chatSession.messageName || '未知用户'
    }

    if (chatStore.existMessages(sessionId)) {
      const data = chatStore.getMessages(sessionId)
      messageList.value = data
    } else {
      const { data } = await getChats(sessionId)
      messageList.value = data
      // 持久化
      chatStore.setMessageAll(sessionId, data)

      // 重新获取会话信息，因为API调用后可能会更新
      const updatedChatSession = chatStore.getChatSession(sessionId)
      if (updatedChatSession) {
        chatInfo.value.name =
          updatedChatSession.messageName || updatedChatSession.nickname || '未知用户'
        console.log('更新聊天名称:', chatInfo.value.name)
      }
    }
  } catch (error) {
    console.error('初始化聊天数据失败:', error)
    chatInfo.value.name = '加载失败'
  }
}

// 处理发送消息
const handleSendMessage = (message) => {
  const data = getSendMessageInfo(chatSessionId.value)
  data.message = message
  try {
    chatStore.sendMessage(JSON.stringify(data))
    // 追加消息列表
    messageList.value.push({
      messageId: Date.now() + Math.random(), // 生成临时的唯一ID
      userId: userInfo.value.userId,
      nickname: userInfo.value.nickname,
      avatar: userInfo.value.avatar,
      content: message.content,
      createTime: new Date()
    })
    // 更改此会话信息
    chatStore.updateSendMsgChatSession(message)
  } catch (e) {
    // todo 发送失败处理，消息列表增加，但是需要加红色感叹号
  }
}

// 监听路由参数变化，immediate: true 确保组件初始化时也会执行
watch(
  () => route.params.id,
  async (newId) => {
    if (newId) {
      console.log('路由参数变化，新的会话ID:', newId)
      chatSessionId.value = Number(newId)
      chatStore.setActiveChatSessionId(chatSessionId.value)

      // 重置聊天信息
      chatInfo.value.name = ''

      // 等待一个tick确保状态更新
      await new Promise((resolve) => setTimeout(resolve, 0))

      await initChatData(chatSessionId.value)
    }
  },
  { immediate: true }
)

// 监听chatStore中的会话列表变化，确保名称能及时更新
watch(
  () => chatStore.chatSession,
  () => {
    if (chatSessionId.value) {
      const chatSession = chatStore.getChatSession(chatSessionId.value)
      if (chatSession && !chatInfo.value.name) {
        chatInfo.value.name = chatSession.messageName || chatSession.nickname || '未知用户'
        console.log('从会话列表更新获取名称:', chatInfo.value.name)
      }
    }
  },
  { deep: true }
)
</script>

<template>
  <div id="privateChat">
    <div class="drag"></div>
    <chatHeader v-model="chatInfo" />
    <!-- 聊天区域 -->
    <div class="chat-content">
      <MessageList v-model="messageList" />
      <messageInputArea @send-message="handleSendMessage" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.drag {
  width: 200px; /* 固定宽度，只覆盖聊天头部的左侧区域 */
  height: 24px;
  background-color: transparent;
  -webkit-app-region: drag;
  position: absolute;
  top: 0;
  left: 50%; /* 居中定位 */
  transform: translateX(-50%); /* 居中对齐 */
  z-index: 100;
}

#privateChat {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f2f2f2;
  position: relative;
  box-sizing: border-box;

  .chat-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: #f2f2f2;
    min-height: 0; /* 允许flex子元素收缩 */
    overflow: hidden; /* 防止内容溢出 */

    // 消息列表区域占用剩余空间
    :deep(#messageList) {
      flex: 1;
      overflow: hidden;
      /* 防止内容溢出 */
    }

    // 消息输入区域样式 - 让组件自己控制样式
    :deep(.message-input-area) {
      flex: none; // 不参与flex伸缩
      /* 让组件自己决定其他样式 */
    }
  }
}
</style>
