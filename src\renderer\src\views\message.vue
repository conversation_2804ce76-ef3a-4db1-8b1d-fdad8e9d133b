<script setup>
import { ref, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import Search from '../components/search.vue'
import { useChatStore } from '../store/chatStore'
import { datetimeCope } from '../utils/global'

const chatStore = useChatStore()
const router = useRouter()
const route = useRoute()

const chatSessionList = ref([])

// 控制加号选项的显示
const showAddOptions = ref(false)

// 控制搜索框页面的显示
const showSearchPage = ref(false)

// 点击消息项跳转到聊天页面
const openChat = (item) => {
  const currentRoute = route.path
  const targetRoute =
    item.participantType === 2
      ? `/message/chat/${item.chatSessionId}`
      : `/message/privateChat/${item.chatSessionId}`

  // 如果点击的是当前已经打开的聊天会话，强制重新加载，并更新会话列表
  if (currentRoute === targetRoute) {
    // 先跳转到一个临时路由，然后再跳转回来，触发组件重新初始化
    router.push('/message').then(() => {
      if (item.participantType === 2) {
        router.push({ name: 'chat', params: { id: item.chatSessionId } })
      } else {
        router.push({ name: 'privateChat', params: { id: item.chatSessionId } })
      }
    })
  } else {
    // 正常跳转
    if (item.participantType === 2) {
      router.push({ name: 'chat', params: { id: item.chatSessionId } })
    } else {
      router.push({ name: 'privateChat', params: { id: item.chatSessionId } })
    }
  }
}

// 监听会话列表变化
watch(
  () => chatStore.chatSession,
  (newVal) => {
    chatSessionList.value = newVal
  },
  { immediate: true }
)

onMounted(() => {
  chatSessionList.value = chatStore.chatSession
})
</script>

<template>
  <div class="message">
    <div class="left">
      <div class="drag"></div>
      <div class="title">
        <el-input
          v-model="input4"
          style="width: 220px; margin-left: 10px; height: 28px"
          placeholder="搜索"
          @click="showSearchPage = !showSearchPage"
        >
          <template #prefix>
            <svg class="icon" aria-hidden="true">
              <use xlink:href="#icon-sousuo"></use>
            </svg>
          </template>
        </el-input>
        <div class="add-button">
          <div class="jiajia" @click.stop="showAddOptions = !showAddOptions">
            <svg class="icon" aria-hidden="true">
              <use xlink:href="#icon-jiajia"></use>
            </svg>
          </div>
          <!-- 添加选项框 -->
          <div v-show="showAddOptions" class="add-options">
            <div class="option-item" @click="handleOptionClick('group')">
              <svg class="icon" aria-hidden="true">
                <use xlink:href="#icon-qunliao"></use>
              </svg>
              <span>发起群聊</span>
            </div>
            <div class="option-item" @click="handleOptionClick('friend')">
              <svg class="icon" aria-hidden="true">
                <use xlink:href="#icon-haoyou"></use>
              </svg>
              <span>加好友/群</span>
            </div>
          </div>
        </div>
      </div>
      <div v-if="!showSearchPage" class="chatSessionListClass">
        <div
          v-for="item in chatSessionList"
          :key="item.chatSessionId"
          class="message-item"
          @click="openChat(item)"
        >
          <img :src="item.avatar" />
          <div class="message-content">
            <div class="message-name">{{ item.messageName }}</div>
            <div class="message-message">
              {{ (item.nickname ?? '') ? item.nickname + '：' : '' }}{{ item.content }}
            </div>
          </div>
          <div class="message-right">
            <div class="message-time">{{ datetimeCope(item.latestMessageTime) }}</div>
            <div v-if="item.unreadCount > 0" class="message-unread">{{ item.unreadCount }}</div>
          </div>
        </div>
      </div>
      <div v-else>
        <Search />
      </div>
    </div>
    <div class="right">
      <router-view />
      <div v-if="!$route.params.id" class="empty-chat">
        <div class="empty-content">
          <div class="empty-icon">💬</div>
          <div class="empty-text">选择一个聊天开始对话</div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.message {
  display: flex;
  flex-direction: row;
  height: 100vh;
}

.drag {
  width: 280px; /* 统一为280px，与左侧边栏宽度保持一致 */
  height: 20px;
  background-color: #fff;
  -webkit-app-region: drag;
}

.left {
  width: 280px;
  height: 100%;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  border-right: 1px solid #eee;
}

.right {
  flex: 1;
  height: 100%;
  background-color: #f5f6f7;
}

.title {
  padding: 10px;
  display: flex;
  align-items: center;
}

.add-button {
  position: relative;
  margin-left: 10px;
  cursor: pointer;

  .jajjia {
    font-size: 20px;
    color: #999;

    &:hover {
      color: #666;
    }
  }
}

.add-options {
  position: absolute;
  top: 100%;
  right: 0;
  width: 120px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  padding: 4px 0;

  .option-item {
    padding: 8px 16px;
    display: flex;
    align-items: center;
    cursor: pointer;

    &:hover {
      background: #f5f5f5;
    }

    i {
      margin-right: 8px;
      font-size: 16px;
      color: #666;
    }

    span {
      font-size: 13px;
      color: #333;
    }
  }
}

.chatSessionListClass {
  flex: 1;
  overflow-y: auto;

  /* 默认隐藏滚动条 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: transparent;
    border-radius: 3px;
  }

  /* 悬停时显示滚动条 */
  &:hover {
    &::-webkit-scrollbar-thumb {
      background: #d9d9d9;

      &:hover {
        background: #bfbfbf;
      }

      &:active {
        background: #999;
      }
    }
  }
}

.message-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  height: 45px;
  cursor: pointer;

  &:hover {
    background-color: #f5f5f5;
  }
}

.message-item img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 12px;
  flex-shrink: 0;
}

.message-content {
  flex: 1;
  min-width: 0;
}

.message-name {
  font-size: 13px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.message-message {
  font-size: 11px;
  color: #999;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.message-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  margin-left: 8px;
}

.message-time {
  font-size: 11px;
  color: #999;
  margin-bottom: 4px;
}

.message-unread {
  background-color: #cccccc;
  color: white;
  border-radius: 10px;
  padding: 2px 2px;
  font-size: 11px;
  min-width: 16px;
  text-align: center;
  line-height: 1.2;
}

.empty-chat {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;

  .empty-content {
    text-align: center;

    .empty-icon {
      font-size: 48px;
      margin-bottom: 16px;
      opacity: 0.5;
    }

    .empty-text {
      font-size: 16px;
      color: #999;
    }
  }
}
</style>
