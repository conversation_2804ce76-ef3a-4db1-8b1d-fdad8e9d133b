<template>
  <div class="message-input-area" :style="{ height: inputAreaHeight + 'px' }">
    <!-- 可拖拽的调整条 -->
    <div class="resize-handle" @mousedown="startResize"></div>

    <!-- 消息输入区域容器 -->
    <div class="message-input-container">
      <!-- 顶部工具栏 -->
      <div class="input-toolbar">
        <button class="toolbar-btn">
          <svg class="icon" aria-hidden="true">
            <use xlink:href="#icon-biaoqing"></use>
          </svg>
        </button>
        <button class="toolbar-btn">
          <svg class="icon" aria-hidden="true">
            <use xlink:href="#icon-jurassic_scissors"></use>
          </svg>
        </button>
        <button class="toolbar-btn">
          <svg class="icon" aria-hidden="true">
            <use xlink:href="#icon-wenjian"></use>
          </svg>
        </button>
        <button class="toolbar-btn">
          <svg class="icon" aria-hidden="true">
            <use xlink:href="#icon-tupian_o"></use>
          </svg>
        </button>
        <button class="toolbar-btn">
          <svg class="icon" aria-hidden="true">
            <use xlink:href="#icon-hongbao"></use>
          </svg>
        </button>
        <button class="toolbar-btn">
          <svg class="icon" aria-hidden="true">
            <use xlink:href="#icon-yuyintonghua"></use>
          </svg>
        </button>
        <div class="spacer"></div>
        <button class="toolbar-btn">
          <svg class="icon" aria-hidden="true">
            <use xlink:href="#icon-shizhong"></use>
          </svg>
        </button>
      </div>

      <!-- 中间可扩展的文本输入区域 -->
      <div class="input-content">
        <textarea
          v-model="messageInput"
          placeholder="请输入消息..."
          @keyup.enter.exact="sendMessage"
          @keyup.enter.ctrl="messageInput += '\n'"
        ></textarea>
      </div>

      <!-- 底部固定的操作区域 -->
      <div class="input-actions">
        <span class="input-tip">按Enter发送消息，Ctrl+Enter换行</span>
        <button class="send-btn" @click="sendMessage">发送</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onUnmounted } from 'vue'

// 消息输入
const messageInput = ref('')

// 输入区域高度控制
const inputAreaHeight = ref(180) // 默认高度
const minHeight = 180 // 最小高度
const maxHeight = 300 // 最大高度
const isResizing = ref(false)
const startY = ref(0)
const startHeight = ref(0)

// 定义 emit
const emit = defineEmits(['send-message'])

// 发送消息
const sendMessage = () => {
  if (messageInput.value.trim()) {
    emit('send-message', {
      content: messageInput.value
    })
    messageInput.value = ''
  }
}

// 开始调整大小
const startResize = (e) => {
  isResizing.value = true
  startY.value = e.clientY
  startHeight.value = inputAreaHeight.value

  document.addEventListener('mousemove', handleResize)
  document.addEventListener('mouseup', stopResize)

  // 防止文本选择
  e.preventDefault()
}

// 处理调整大小
const handleResize = (e) => {
  if (!isResizing.value) return

  const deltaY = e.clientY - startY.value // 向下拖拽为正值，向上拖拽为负值
  const newHeight = startHeight.value - deltaY // 向上拖拽增加高度，向下拖拽减少高度

  // 限制在最小和最大高度之间
  if (newHeight >= minHeight && newHeight <= maxHeight) {
    inputAreaHeight.value = newHeight
  }
}

// 停止调整大小
const stopResize = () => {
  isResizing.value = false
  document.removeEventListener('mousemove', handleResize)
  document.removeEventListener('mouseup', stopResize)
}

// 清理事件监听器
onUnmounted(() => {
  document.removeEventListener('mousemove', handleResize)
  document.removeEventListener('mouseup', stopResize)
})
</script>

<style scoped>
/* 使用更高优先级的选择器 */
div.message-input-area {
  /* 重置所有可能的外边距和内边距 */
  margin: 0 !important;
  padding: 0 !important;
  box-sizing: border-box !important;
  position: relative !important;
  /* 高度由内联样式控制 */
  display: flex !important;
  flex-direction: column !important;
  min-height: 150px !important;

  .resize-handle {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background-color: transparent;
    cursor: ns-resize;
    z-index: 10;
    flex-shrink: 0;

    /* 添加一个小的视觉指示器 */
    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 40px;
      height: 2px;
      background-color: #d9d9d9;
      border-radius: 1px;
      transition: all 0.2s ease;
    }

    &:hover {
      background-color: rgba(24, 144, 255, 0.1);

      &::before {
        background-color: #1890ff;
        width: 60px;
        height: 3px;
      }
    }

    &:active {
      background-color: rgba(24, 144, 255, 0.2);

      &::before {
        background-color: #1890ff;
        width: 80px;
        height: 3px;
      }
    }
  }

  .message-input-container {
    display: flex !important;
    flex-direction: column !important;
    box-sizing: border-box !important;
    flex: 1 !important;
    /* 占满剩余空间 */
    padding-top: 6px !important;
    /* 为拖拽条留出空间 */
    background-color: #f2f2f2 !important;

    .input-toolbar {
      display: flex !important;
      align-items: center !important;
      padding: 4px 16px !important; /* 减少垂直内边距，与聊天头部一致 */
      border-bottom: 1px solid #f0f0f0 !important;
      flex-shrink: 0 !important;
      min-height: 38px !important; /* 调整最小高度 */
      gap: 2px !important; /* 添加统一的按钮间距 */

      .toolbar-btn {
        width: 30px; /* 与聊天头部按钮尺寸一致 */
        height: 30px;
        border: none;
        background: transparent;
        cursor: pointer;
        margin-right: 0; /* 移除右边距，使用gap控制间距 */
        border-radius: 4px; /* 与聊天头部圆角一致 */
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        transition: all 0.15s ease; /* 添加过渡效果 */
        padding: 0;

        &:hover {
          background-color: rgba(0, 0, 0, 0.05); /* 与聊天头部悬停效果一致 */
        }

        &:active {
          background-color: rgba(0, 0, 0, 0.08);
          transform: scale(0.96); /* 添加点击效果 */
        }

        .icon {
          width: 18px; /* 与聊天头部图标尺寸一致 */
          height: 18px;
          fill: #666;
          pointer-events: none;
        }
      }

      .spacer {
        flex: 1;
      }
    }

    /* 中间可扩展的文本输入区域 */
    .input-content {
      flex: 1;
      padding: 8px 16px; /* 左右内边距与底部操作区域保持一致 */
      display: flex;
      flex-direction: column;

      textarea {
        width: 100%;
        min-height: 40px;
        max-height: 120px;
        border: none;
        outline: none;
        resize: none;
        font-size: 14px;
        line-height: 1.4;
        font-family: inherit;
        background-color: #f2f2f2;
        flex: 1;
      }
    }

    /* 底部固定的操作区域 */
    .input-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px; /* 增加内边距，左右从10px增加到16px，上下从6px增加到12px */
      border-top: 1px solid #f0f0f0;
      background-color: #f2f2f2;
      flex-shrink: 0;
      /* 防止被压缩 */

      .input-tip {
        font-size: 12px;
        color: #999;
      }

      .send-btn {
        padding: 6px 16px;
        background-color: #1890ff;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 13px;

        &:hover {
          background-color: #40a9ff;
        }
      }
    }
  }
}
</style>
