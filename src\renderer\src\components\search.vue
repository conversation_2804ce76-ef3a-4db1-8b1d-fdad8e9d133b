<script setup>

const netSearch = () => {
    window.electron.ipcRenderer.send('net-search')
}

</script>

<template>
    <div class="search">
        <div class="advise">
            <div>搜索建议</div>
            <div class="advise-content">
                <div>@我</div>
                <div>特别关系</div>
            </div>
        </div>
        <div class="net-search" @click="netSearch">
            <div class="search-icon">
                <svg class="icon" aria-hidden="true">
                    <use xlink:href="#icon-sousuo"></use>
                </svg>
            </div>
            <div class="search-content">
                <div class="search-title">进入全网搜索</div>
                <div class="search-desc">查找用户、群聊等</div>
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.search {
    width: 100%;
    height: 100%;
    background-color: #fff;
    padding: 16px;
    box-sizing: border-box;
    overflow: hidden;

    .advise {
        border-bottom: 1px solid #eee;
        padding-bottom: 16px;
        margin-bottom: 16px;

        > div:first-child {
            font-size: 13px;
            color: #999;
            margin-bottom: 12px;
        }

        .advise-content {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;

            div {
                padding: 6px 12px;
                background-color: #f5f5f5;
                border-radius: 16px;
                cursor: pointer;
                font-size: 13px;
                color: #666;
                transition: background-color 0.2s ease;

                &:hover {
                    background-color: #e8e8e8;
                }
            }
        }
    }

    .net-search {
        display: flex;
        align-items: center;
        padding: 12px;
        cursor: pointer;
        border-radius: 8px;
        transition: background-color 0.2s ease;
        width: 100%;
        box-sizing: border-box;

        &:hover {
            background-color: #f5f5f5;
        }

        .search-icon {
            width: 36px;
            height: 36px;
            background-color: #1890ff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            flex-shrink: 0;

            .icon {
                width: 18px;
                height: 18px;
                color: white;
            }
        }

        .search-content {
            display: flex;
            flex-direction: column;
            flex: 1;
            min-width: 0;

            .search-title {
                font-size: 14px;
                color: #333;
                font-weight: 500;
                margin-bottom: 2px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .search-desc {
                font-size: 12px;
                color: #999;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }
    }
}
</style>