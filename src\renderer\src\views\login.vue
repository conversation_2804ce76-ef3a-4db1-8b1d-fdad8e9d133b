<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { login, register } from '../api/login'
import { getChatSession } from '../api/chatSession';
import { ElMessage } from 'element-plus';
import { useChatStore } from '../store/chatStore'

const router = useRouter()
const chatStore = useChatStore()

// todo 两次密码验证
const form = ref({
  username: '',
  password: '',
  rePassword: ''
})

// 用户信息
const loginUser = ref()

// 登录和注册调用
const handleLogin = async () => {
  if (isRegister.value) {
    await register(form.value)
    ElMessage({
      showClose: true,
      message: '注册成功',
      type: 'success'
    })
    isRegister.value = false
  } else {
    const data = await login(form.value)
    loginUser.value = data.data;
    console.log("用户信息：", loginUser.value);

    // 设置到 Pinia store
    chatStore.setUserInfo(loginUser.value)

    // 同时保存到 localStorage，供主进程获取
    localStorage.setItem('userInfo', JSON.stringify(loginUser.value))
    if (loginUser.value.userId) {
      try {
        // 等待WebSocket连接建立成功
        await chatStore.initWebSocket(loginUser.value.userId)
        const res = await getChatSession();
        console.log('获取聊天会话成功:', res.data)
        chatStore.setChatSession(res.data)

        router.push("/home")
        // 发送登录事件，调整窗口大小
        window.electron.ipcRenderer.send("login")
      } catch (error) {
        console.error('WebSocket连接失败:', error)
        ElMessage({
          showClose: true,
          message: 'WebSocket连接失败，请检查网络连接',
          type: 'error'
        })
      }
    } else {
      ElMessage({
        showClose: true,
        message: '登录失败，请重新登录',
        type: 'error'
      })
    }
  }
}

const isRegister = ref(false)
const changeRegister = () => {
  isRegister.value = !isRegister.value
}

const photo = ref('https://q1.qlogo.cn/g?b=qq&nk=2029627162&s=640')

// 添加关闭窗口处理函数
const handleClose = () => {
  window.electron.ipcRenderer.send('close-window')
}
</script>

<template>
  <div class="login">
    <div class="drag">
      <i class="iconfont icon-fork close-icon" @click="handleClose"></i>
    </div>
    <div class="container">
      <div class="photo">
        <img :src="photo" />
      </div>
      <div class="form-container">
        <el-input v-model="form.username" placeholder="输入账号">
          <template #prefix>
            <div class="placeholder-spacer"></div>
          </template>
        </el-input>
        <el-input v-model="form.password" type="password" placeholder="输入密码">
          <template #prefix>
            <div class="placeholder-spacer"></div>
          </template>
        </el-input>
        <el-input v-if="isRegister" v-model="form.rePassword" type="password" placeholder="确认密码">
          <template #prefix>
            <div class="placeholder-spacer"></div>
          </template>
        </el-input>
      </div>
      <el-button type="primary" class="login-btn" @click="handleLogin">{{ isRegister ? '注册' : '登录' }}</el-button>
      <div class="footer">
        <span>扫码登录</span>
        <span @click="changeRegister">注册账户</span>
        <span>找回密码</span>
      </div>
      <el-checkbox class="agreement">
        已阅读并同意服务协议和隐私保护指引
      </el-checkbox>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.login {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(180deg, #e7f3ff 0%, #ffffff 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  position: fixed;
  top: 0;
  left: 0;
  overflow: hidden;
}

.drag {
  -webkit-app-region: drag;
  width: 100%;
  height: 30px;
  position: relative;
}

.close-icon {
  position: absolute;
  right: 0;
  top: 0;
  font-size: 15px;
  color: #000;
  cursor: pointer;
  -webkit-app-region: no-drag;
  padding: 5px 10px;

  &:hover {
    background-color: red;
  }
}

.container {
  width: 250px;
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  max-height: calc(100vh - 50px);
  overflow: hidden;
}

.photo {
  width: 65px;
  height: 65px;
  border-radius: 50%;
  overflow: hidden;
  margin-bottom: 15px;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.form-container {
  width: 100%;

  :deep(.el-input) {
    margin-bottom: 12px;

    .el-input__wrapper {
      border-radius: 6px;
      background-color: rgba(255, 255, 255, 0.8);
      padding: 0;
      height: 42px;
      box-shadow: none;
    }

    .el-input__inner {
      height: 42px;
      text-align: center;
      color: #333;
      font-size: 14px;

      &::placeholder {
        text-align: center;
        color: #999;
      }
    }

    .placeholder-spacer {
      width: 12px;
    }
  }
}

.login-btn {
  width: 100%;
  height: 35px;
  border-radius: 6px;
  margin: 10px 0;
  background: #5eacff;
  font-size: 14px;

  &:hover {
    background: #4a99ff;
  }
}

.footer {
  width: 100%;
  display: flex;
  justify-content: space-between;
  color: #5eacff;
  font-size: 11px;
  margin: 8px 0;

  span {
    cursor: pointer;

    &:hover {
      color: #4a99ff;
    }
  }
}

.agreement {
  font-size: 11px;
  color: #666;
  margin-top: 15px;
  margin-bottom: 10px;

  :deep(.el-checkbox__label) {
    font-size: 11px;
  }
}
</style>
