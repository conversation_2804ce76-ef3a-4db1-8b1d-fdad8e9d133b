<script setup>
import { ref, computed, onMounted, onUnmounted } from "vue";
import { useRouter } from "vue-router";
import Base from "../components/base.vue";
import userinfo from "../components/user/userinfo.vue";
import { useChatStore } from "../store/chatStore";

const chatStore = useChatStore()
const router = useRouter();

const activeIcon = ref("message");
const showProfile = ref(false);

const setActive = (icon) => {
  activeIcon.value = icon;

  switch (icon) {
    case "message":
      router.push("/home/<USER>");
      break;
    case "contact":
      router.push("/home/<USER>");
      break;
    case "shoucang":
      router.push("/home/<USER>");
      break;
  }
};

const toggleProfile = () => {
  showProfile.value = !showProfile.value;
};

// 处理点击外部区域隐藏个人信息卡片
const handleOutsideClick = () => {
  if (showProfile.value) {
    showProfile.value = false;
  }
};

// 使用 computed 让 userInfo 响应式地监听 store 变化
const userInfo = computed(() => chatStore.getUserInfo())

// 监听 localStorage 变化，用于 Electron 多窗口数据同步
const handleStorageChange = (event) => {
  if (event.key === 'userInfo' && event.newValue) {
    try {
      const updatedUserInfo = JSON.parse(event.newValue);
      chatStore.setUserInfo(updatedUserInfo);
    } catch (error) {
      console.error('解析 localStorage userInfo 失败:', error);
    }
  }
};

// 监听主进程发送的用户信息更新
const handleUserInfoUpdate = (event, userData) => {
  if (userData) {
    chatStore.setUserInfo(userData);
    // 同时更新 localStorage 保持一致性
    localStorage.setItem('userInfo', JSON.stringify(userData));
  }
};

onMounted(() => {
  // 监听 localStorage 变化事件
  window.addEventListener('storage', handleStorageChange);

  // 如果在 Electron 环境中，也监听主进程通信
  if (window.electron && window.electron.ipcRenderer) {
    window.electron.ipcRenderer.on('update-user-info', handleUserInfoUpdate);
  }
});

onUnmounted(() => {
  // 清理事件监听器
  window.removeEventListener('storage', handleStorageChange);

  if (window.electron && window.electron.ipcRenderer) {
    window.electron.ipcRenderer.removeListener('update-user-info', handleUserInfoUpdate);
  }
});
</script>

<template>
  <div class="home" @click="handleOutsideClick">
    <!-- 窗口控制按钮 -->
    <Base />
    <div class="left">
      <div class="drag"></div>
      <div class="logo">QQ</div>
      <img :src="userInfo?.avatar" alt="" @click.stop="toggleProfile" />
      <!-- 个人信息卡片 -->
      <template v-if="showProfile">
        <userinfo @click.stop></userinfo>
      </template>
      <svg class="icon outerColor leftIcon" aria-hidden="true" :class="{ active: activeIcon === 'message' }"
        @click="setActive('message')">
        <use :xlink:href="`#icon-xiaoxi`"></use>
      </svg>
      <svg class="icon outerColor leftIcon" aria-hidden="true" :class="{ active: activeIcon === 'contact' }"
        @click="setActive('contact')">
        <use :xlink:href="`#icon-renwu`"></use>
      </svg>
      <svg class="icon outerColor leftIcon" aria-hidden="true" :class="{ active: activeIcon === 'shoucang' }"
        @click="setActive('shoucang')">
        <use :xlink:href="`#icon-biaoqianA01_shoucang-49`"></use>
      </svg>
    </div>
    <div class="right">
      <router-view></router-view>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.signature {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.drag {
  width: 60px;
  height: 20px;
  background-color: #f2f2f2;
  -webkit-app-region: drag;
}

.home {
  display: flex;
  flex-direction: row;
  height: 100vh;
  background-color: #fff;
  margin: 0;
  padding: 0;
}

.left {
  width: 60px;
  height: calc(100vh);
  background-color: #f2f2f2;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.logo {
  font-size: 20px;
  color: #000;
  margin-bottom: 30px;
}

img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-bottom: 30px;
  object-fit: cover;
}

.leftIcon {
  font-size: 24px;
  color: #000;
  margin: 15px 0;
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: #fff;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }

  &:active {
    transform: translateY(0);
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  &.active {
    color: #409eff;
    background-color: rgba(64, 158, 255, 0.1);
  }
}

.right {
  width: calc(100vw - 60px);
  height: 100vh;
  background-color: #f5f6f7;
}
</style>
