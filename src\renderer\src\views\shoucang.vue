<script setup>
import { ref, computed } from 'vue'
import ShowItem from '../components/ShowItem.vue'
import Base from '../components/base.vue'

const searchQuery = ref('')
const selectedTab = ref('全部')
const tabs = ['全部', '聊天记录', '图片与视频', '文件', '链接', '笔记', '其他']

const favoriteList = ref([
  {
    name: '【200篇】阿里面经.pdf',
    size: '1.9 MB',
    category: '文档',
    favoriteTime: '03-13 13:30',
    source: '考研就业交流群'
  },
  {
    name: '【200篇】百度面经合集.pdf',
    size: '7.7 MB',
    category: '文档',
    favoriteTime: '03-13 13:30',
    source: '考研就业交流群'
  },
  {
    name: '【200篇】美团面经合集.pdf',
    size: '6.7 MB',
    category: '文档',
    favoriteTime: '03-13 13:29',
    source: '考研就业交流群'
  },
  {
    name: '【200篇】腾讯面经合集.pdf',
    size: '9.7 MB',
    category: '文档',
    favoriteTime: '03-13 13:29',
    source: '考研就业交流群'
  },
  {
    name: '【200篇】字节技术岗位面经合集.pdf',
    size: '9.0 MB',
    category: '文档',
    favoriteTime: '03-13 13:29',
    source: '考研就业交流群'
  }
])

const filteredList = computed(() => {
  return favoriteList.value.filter((item) => {
    const matchesSearch = item.name.toLowerCase().includes(searchQuery.value.toLowerCase())
    const matchesTab = selectedTab.value === '全部' || item.category === selectedTab.value
    return matchesSearch && matchesTab
  })
})
</script>

<template>
  <div class="shoucang-container">
    <div class="left-sidebar">
      <div class="search-input">
        <svg class="icon outerColor" aria-hidden="true">
          <use xlink:href="#icon-sousuo"></use>
        </svg>
        <input v-model="searchQuery" type="text" placeholder="搜索收藏内容" />
      </div>
      <div class="tab-list">
        <div
          v-for="tab in tabs"
          :key="tab"
          :class="['tab-item', { active: selectedTab === tab }]"
          @click="selectedTab = tab"
        >
          {{ tab }}
        </div>
      </div>
    </div>

    <div class="right-content">
      <Base />
      <div class="favorite-list">
        <ShowItem
          v-for="item in filteredList"
          :key="item.name"
          :icon="`icon-${item.category.toLowerCase()}`"
          :name="item.name"
          :size="item.size"
          :time="item.favoriteTime"
          :source="item.source"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.shoucang-container {
  display: flex;
  height: 100%;
  background-color: #f5f6f7;
}

.left-sidebar {
  width: 248px; /* 280px - 32px(padding) = 248px，确保总宽度与其他页面一致 */
  padding: 16px;
  border-right: 1px solid #e0e0e0;
  background-color: #fff;

  .search-input {
    display: flex;
    align-items: center;
    background-color: #f5f6f7;
    border-radius: 6px;
    padding: 8px 12px;
    margin-bottom: 16px;

    .icon {
      font-size: 18px;
      color: #8a8a8a;
      margin-right: 8px;
    }

    input {
      flex: 1;
      border: none;
      background: none;
      outline: none;
      font-size: 14px;
      color: #333;

      &::placeholder {
        color: #999;
      }
    }
  }

  .tab-list {
    .tab-item {
      padding: 10px 14px;
      font-size: 14px;
      color: #666;
      cursor: pointer;
      border-radius: 6px;
      margin-bottom: 4px;
      transition: all 0.2s ease;

      &:hover {
        background-color: #f5f6f7;
        color: #333;
      }

      &.active {
        background-color: #e6f4ff;
        color: #1296db;
      }
    }
  }
}

.right-content {
  flex: 1;
  padding: 46px 16px 16px;
  overflow-y: auto;
  position: relative;

  .favorite-list {
    max-width: 800px;
    margin: 0 auto;
  }
}
</style>
