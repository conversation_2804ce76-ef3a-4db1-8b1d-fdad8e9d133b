<script setup>
import { ref, computed } from 'vue'
import Base from '../components/base.vue'
import { searchUsers } from '../api/user'
import { searchGroups } from '../api/group'

const searchText = ref('')
const activeTab = ref('全部')

const tabs = ['全部', '用户', '群聊', '机器人']

// 用户搜索结果
const userResults = ref([])

// 群聊搜索结果
const groupResults = ref([])

// 计算属性：根据当前标签显示对应的数据
const displayUsers = computed(() => {
  if (activeTab.value === '全部') {
    return userResults.value.slice(0, 3) // 全部标签只显示前3个
  }
  return userResults.value
})

const displayGroups = computed(() => {
  if (activeTab.value === '全部') {
    return groupResults.value.slice(0, 3) // 全部标签只显示前3个
  }
  return groupResults.value
})

// 计算是否显示空状态
const showEmptyState = computed(() => {
  // 如果没有搜索关键词，显示空状态
  if (!searchText.value.trim()) {
    return true
  }

  // 如果有搜索关键词但没有结果，也显示空状态
  const hasUserResults = userResults.value.length > 0
  const hasGroupResults = groupResults.value.length > 0

  if (activeTab.value === '全部') {
    return !hasUserResults && !hasGroupResults
  } else if (activeTab.value === '用户') {
    return !hasUserResults
  } else if (activeTab.value === '群聊') {
    return !hasGroupResults
  }

  return true
})

const handleSearch = async () => {
  if (activeTab.value === '用户') {
    userResults.value = (await searchUsers(searchText.value)).data
  } else if (activeTab.value === '群聊') {
    groupResults.value = (await searchGroups(searchText.value)).data
  } else if (activeTab.value === '全部') {
    userResults.value = (await searchUsers(searchText.value)).data
    groupResults.value = (await searchGroups(searchText.value)).data
  }
}

const joinGroup = (group) => {
  // 只传递必要的数据，避免序列化问题
  const groupData = {
    groupId: group.groupId,
    groupName: group.groupName,
    avatar: group.avatar,
    currentCount: group.currentCount,
    countLimit: group.countLimit
  }
  window.electron.ipcRenderer.send('add-group', groupData)
}

const addUser = (user) => {
  // 只传递必要的数据，避免序列化问题
  const userData = {
    userId: user.userId,
    nickname: user.nickname,
    account: user.account,
    avatar: user.avatar
  }
  window.electron.ipcRenderer.send('add-user', userData)
}

const changeTab = (tab) => {
  activeTab.value = tab
  if (searchText.value !== '') {
    handleSearch()
  }
}
</script>

<template>
  <div class="netsearch">
    <!-- 窗口控制按钮 -->
    <Base class="base" />

    <!-- 标题栏 -->
    <div class="header">
      <div class="title">全网搜索</div>
    </div>

    <!-- 搜索框 -->
    <div class="search-container">
      <div class="search-box">
        <svg class="icon search-icon" aria-hidden="true">
          <use xlink:href="#icon-sousuo"></use>
        </svg>
        <input v-model="searchText" type="text" placeholder="搜索" @keyup.enter="handleSearch" />
        <button v-if="searchText" class="clear-btn" @click="searchText = ''">×</button>
      </div>
    </div>

    <!-- 标签页 -->
    <div class="tabs">
      <div
        v-for="tab in tabs"
        :key="tab"
        :class="['tab-item', { active: activeTab === tab }]"
        @click="changeTab(tab)"
      >
        {{ tab }}
      </div>
    </div>

    <!-- 搜索结果列表 -->
    <div class="result-list">
      <!-- 空状态 -->
      <div v-if="showEmptyState" class="empty-state">
        <svg class="empty-icon" aria-hidden="true">
          <use xlink:href="#icon--none-search-copy"></use>
        </svg>
        <div class="empty-text">输入关键词搜索</div>
      </div>

      <!-- 有数据时显示结果 -->
      <template v-else>
        <!-- 用户结果 -->
        <div v-if="activeTab === '全部' || activeTab === '用户'" class="user-section">
          <div v-if="activeTab === '全部' && userResults.length > 0" class="section-header">
            <span class="section-title">用户</span>
            <span class="more-link" @click="activeTab = '用户'">更多</span>
          </div>
          <div class="user-list">
            <div v-for="user in displayUsers" :key="user.userId" class="user-item">
              <img :src="user.avatar" :alt="user.name" class="user-avatar" />
              <div class="user-info">
                <div class="user-name">{{ user.nickname }}</div>
                <div class="user-id">{{ user.account }}</div>
              </div>
              <button class="add-btn" @click="addUser(user)">添加</button>
            </div>
          </div>
        </div>

        <!-- 群聊结果 -->
        <div v-if="activeTab === '全部' || activeTab === '群聊'" class="group-section">
          <div v-if="activeTab === '全部' && groupResults.length > 0" class="section-header">
            <span class="section-title">群聊</span>
            <span class="more-link" @click="activeTab = '群聊'">更多</span>
          </div>
          <div class="group-list">
            <div v-for="group in displayGroups" :key="group.groupId" class="group-item">
              <div class="group-avatar">
                <img :src="group.avatar" :alt="group.groupName" />
              </div>
              <div class="group-info">
                <div class="group-name">{{ group.groupName }}</div>
                <div class="group-meta">
                  <span class="member-icon">👥</span>
                  <span class="member-count">{{ group.currentCount }}/{{ group.countLimit }}</span>
                </div>
              </div>
              <button class="join-btn" @click="joinGroup(group)">加入</button>
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.netsearch {
  width: 100%;
  height: 100vh;
  background-color: #fff;
  display: flex;
  flex-direction: column;

  .base {
    background-color: #fff;
  }
}

.header {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 4px 16px;
  /* 减少上下padding */
  height: 24px;
  /* 减少高度 */
  position: relative;

  .title {
    font-size: 14px;
    color: #333;
    font-weight: 400;
  }
}

/* 创建一个拖拽区域，但不覆盖右上角 */
.header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 90px;
  /* 为控制按钮留出空间 */
  bottom: 0;
  -webkit-app-region: drag;
  z-index: -1;
  /* 确保在内容下方 */
}

.search-container {
  padding: 6px 16px 12px 16px;

  .search-box {
    position: relative;
    display: flex;
    align-items: center;
    background-color: #f5f5f5;
    border-radius: 20px;
    padding: 6px 16px;

    .search-icon {
      margin-right: 8px;
      color: #999;
      font-size: 14px;
    }

    input {
      flex: 1;
      border: none;
      background: transparent;
      outline: none;
      font-size: 14px;
      color: #333;

      &::placeholder {
        color: #999;
      }
    }

    .clear-btn {
      background: none;
      border: none;
      color: #999;
      cursor: pointer;
      font-size: 16px;
      padding: 0;
      margin-left: 8px;

      &:hover {
        color: #666;
      }
    }
  }
}

.tabs {
  display: flex;
  padding: 0 20px;
  border-bottom: 1px solid #f0f0f0;

  .tab-item {
    padding: 12px 16px;
    cursor: pointer;
    font-size: 14px;
    color: #666;
    border-bottom: 2px solid transparent;

    &.active {
      color: #1890ff;
      border-bottom-color: #1890ff;
    }

    &:hover {
      color: #1890ff;
    }
  }
}

.result-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px 16px;

  /* 自定义滚动条 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: #d9d9d9;
    border-radius: 3px;

    &:hover {
      background: #bfbfbf;
    }
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 400px;
    color: #999;

    .empty-icon {
      width: 80px;
      height: 80px;
      margin-bottom: 16px;
      color: #d9d9d9;
    }

    .empty-text {
      font-size: 14px;
      color: #666;
    }
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0 8px 0;
    margin-bottom: 8px;

    .section-title {
      font-size: 14px;
      color: #666;
      font-weight: 500;
    }

    .more-link {
      font-size: 12px;
      color: #1890ff;
      cursor: pointer;

      &:hover {
        color: #40a9ff;
      }
    }
  }
}

.user-list {
  margin-bottom: 16px;

  .user-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    margin-bottom: 1px;
    background-color: #fff;
    border-radius: 8px;
    transition: background-color 0.2s;

    &:hover {
      background-color: #f5f5f5;
    }

    .user-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      margin-right: 12px;
      object-fit: cover;
    }

    .user-info {
      flex: 1;

      .user-name {
        font-size: 14px;
        color: #333;
        font-weight: 500;
        margin-bottom: 4px;
      }

      .user-id {
        font-size: 12px;
        color: #999;
      }
    }

    .add-btn {
      padding: 6px 12px;
      background-color: transparent;
      color: #666;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
      flex-shrink: 0;

      &:hover {
        background-color: #f0f0f0;
        border-color: #bbb;
      }
    }
  }
}

.group-list {
  .group-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    margin-bottom: 1px;
    background-color: #fff;

    &:hover {
      background-color: #f8f9fa;
    }

    .group-avatar {
      width: 40px;
      height: 40px;
      margin-right: 12px;
      flex-shrink: 0;

      img {
        width: 100%;
        height: 100%;
        border-radius: 6px;
        object-fit: cover;
      }
    }

    .group-info {
      flex: 1;
      min-width: 0;

      .group-name {
        font-size: 14px;
        color: #333;
        font-weight: 400;
        margin-bottom: 4px;
        line-height: 1.3;
      }

      .group-meta {
        display: flex;
        align-items: center;
        gap: 4px;

        .member-icon {
          font-size: 12px;
          color: #999;
        }

        .member-count {
          font-size: 12px;
          color: #999;
        }
      }
    }

    .join-btn {
      padding: 6px 12px;
      background-color: transparent;
      color: #666;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
      flex-shrink: 0;

      &:hover {
        background-color: #f0f0f0;
        border-color: #bbb;
      }
    }
  }
}
</style>
