<template>
  <div v-if="visible" class="dialog-overlay" @click="handleOverlayClick">
    <div class="agree-dialog" @click.stop>
      <!-- 标题栏 -->
      <div class="header">
        <span class="title">同意好友请求</span>
        <button class="close-btn" @click="closeDialog">
          <svg class="icon" aria-hidden="true">
            <use xlink:href="#icon-fork"></use>
          </svg>
        </button>
      </div>

      <!-- 用户信息 -->
      <div class="user-info">
        <img :src="userInfo.avatar" :alt="userInfo.nickname" class="user-avatar" />
        <div class="user-details">
          <div class="user-name">{{ userInfo.nickname }}</div>
        </div>
      </div>

      <!-- 表单内容 -->
      <div class="form-content">
        <!-- 备注 -->
        <div class="form-group">
          <label class="form-label">备注（选填）</label>
          <input v-model="remark" type="text" class="form-input" :placeholder="userInfo.nickname" />
        </div>

        <!-- 分组 -->
        <div class="form-group">
          <label class="form-label">分组</label>
          <select v-model="selectedGroup" class="form-select">
            <option
              v-for="item in friendGroups"
              :key="item.groupFriendId"
              :value="item.groupFriendId"
            >
              {{ item.groupName }}
            </option>
          </select>
        </div>
      </div>

      <!-- 底部按钮 -->
      <div class="footer">
        <button class="cancel-btn" @click="closeDialog">取消</button>
        <button class="agree-btn" @click="handleAgree">同意</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { getFriendGroups, agreeFriend } from '../api/user'
import { ElMessage } from 'element-plus'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  userInfo: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['close', 'success'])

// 好友分组列表
const friendGroups = ref([])

// 表单数据
const remark = ref('')
const selectedGroup = ref()

// 获取好友分组列表
const loadFriendGroups = async () => {
  try {
    const { data } = await getFriendGroups()
    friendGroups.value = data
    // 默认选择第一个分组
    if (data.length > 0) {
      selectedGroup.value = data[0].groupFriendId
    }
  } catch (error) {
    console.error('获取好友分组失败:', error)
  }
}

// 监听对话框显示状态
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      // 重置表单数据
      remark.value = props.userInfo.nickname || ''
      loadFriendGroups()
    }
  }
)

// 关闭对话框
const closeDialog = () => {
  emit('close')
}

// 点击遮罩层关闭
const handleOverlayClick = () => {
  closeDialog()
}

// 同意好友申请
const handleAgree = async () => {
  try {
    const requestData = {
      userInformId: props.userInfo.userInformId,
      userRemark: remark.value,
      userGroupFriendId: selectedGroup.value
    }

    const { data } = await agreeFriend(requestData)
    if (data) {
      ElMessage({
        showClose: true,
        message: '已同意好友申请',
        type: 'success'
      })
      emit('success')
      closeDialog()
    } else {
      ElMessage({
        showClose: true,
        message: '操作失败',
        type: 'error'
      })
    }
  } catch (error) {
    console.error('同意好友申请失败:', error)
    ElMessage({
      showClose: true,
      message: '操作失败，请重试',
      type: 'error'
    })
  }
}

onMounted(() => {
  loadFriendGroups()
})
</script>

<style lang="scss" scoped>
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.agree-dialog {
  width: 400px;
  background: #f2f2f2;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  animation: dialogFadeIn 0.3s ease-out;
}

@keyframes dialogFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.header {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px 20px;
  background: #f2f2f2;
  position: relative;

  .title {
    font-size: 14px;
    font-weight: 500;
    color: #333;
  }

  .close-btn {
    position: absolute;
    top: 16px;
    right: 20px;
    width: 24px;
    height: 24px;
    border: none;
    background: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: background-color 0.2s;

    &:hover {
      background: #e0e0e0;
    }

    .icon {
      font-size: 14px;
      color: #666;
    }
  }
}

.user-info {
  display: flex;
  align-items: center;
  padding: 20px;
  gap: 12px;

  .user-avatar {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    object-fit: cover;
  }

  .user-details {
    .user-name {
      font-size: 16px;
      font-weight: 500;
      color: #333;
      margin-bottom: 4px;
    }
  }
}

.form-content {
  padding: 0 20px 20px;

  .form-group {
    margin-bottom: 16px;

    .form-label {
      display: block;
      font-size: 14px;
      color: #666;
      margin-bottom: 8px;
      margin-left: 10px;
    }

    .form-input {
      width: 100%;
      padding: 10px 12px;
      border: 1px solid #fff;
      border-radius: 6px;
      font-size: 14px;
      transition: border-color 0.2s;
      box-sizing: border-box;

      &:focus {
        outline: none;
        border-color: #409eff;
      }
    }

    .form-select {
      width: 100%;
      padding: 10px 12px;
      border: 1px solid #fff;
      border-radius: 6px;
      font-size: 14px;
      background: #fff;
      cursor: pointer;
      transition: border-color 0.2s;
      box-sizing: border-box;

      &:focus {
        outline: none;
        border-color: #409eff;
      }
    }
  }
}

.footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 20px;
  border-top: 1px solid #f0f0f0;
  background: #f2f2f2;

  .cancel-btn,
  .agree-btn {
    padding: 8px 20px;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s;
    border: none;
  }

  .cancel-btn {
    background: #f5f5f5;
    color: #666;

    &:hover {
      background: #e0e0e0;
    }
  }

  .agree-btn {
    background: #409eff;
    color: #fff;

    &:hover {
      background: #337ecc;
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
    }

    &:active {
      transform: translateY(0);
    }
  }
}
</style>
