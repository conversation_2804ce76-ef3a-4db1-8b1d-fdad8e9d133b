<script setup>
import { ref, computed, onMounted } from 'vue'
import { statusList } from '../utils/status'
import { changeUserStatus } from '../api/user'

const emit = defineEmits(['close', 'select'])

// 从localStorage获取用户信息
const getUserInfoFromStorage = () => {
  try {
    const storedUserInfo = localStorage.getItem('userInfo')
    return storedUserInfo ? JSON.parse(storedUserInfo) : null
  } catch (error) {
    console.error('获取用户信息失败:', error)
    return null
  }
}

// 根据用户状态初始化当前状态
const initCurrentStatus = () => {
  const userInfo = getUserInfoFromStorage()
  const userStatus = userInfo?.status || '听歌中'
  const statusItem = statusList.find(item => item.text === userStatus) || statusList.find(item => item.text === '听歌中')
  return {
    icon: statusItem.icon,
    text: statusItem.text,
    bgColor: statusItem.bgColor
  }
}

const currentStatus = ref(initCurrentStatus())
const selectedStatus = ref(getUserInfoFromStorage()?.status || '听歌中')

// 计算背景渐变色
const backgroundStyle = computed(() => {
  const baseColor = currentStatus.value.bgColor || '#E91E63'
  // 将颜色转换为更浅的版本
  const lightColor = baseColor + '20' // 添加透明度
  const mediumColor = baseColor + '40'
  return {
    background: `linear-gradient(135deg, ${lightColor} 0%, ${mediumColor} 50%, ${lightColor} 100%)`
  }
})

const handleClose = () => {
  window.electron.ipcRenderer.send('close-status-window')
}

const handleSelect = async (status) => {
  try {
    // 调用后端API修改用户状态
    const { data } = await changeUserStatus(status.text)

    if (data === true) {
      // 后端修改成功，更新前端状态
      selectedStatus.value = status.text
      currentStatus.value = {
        icon: status.icon,
        text: status.text,
        bgColor: status.bgColor
      }

      // 更新localStorage中的用户状态
      const userInfo = getUserInfoFromStorage()
      if (userInfo) {
        userInfo.status = status.text
        localStorage.setItem('userInfo', JSON.stringify(userInfo))

        // 通知主进程状态已更新，让主进程通知其他窗口
        window.electron.ipcRenderer.send('user-status-updated', userInfo)
      }

      emit('select', status)
    } else {
      // 后端修改失败，提示用户
      console.error('状态修改失败')
      // 这里可以添加用户提示，比如使用 ElMessage 或其他提示组件
    }
  } catch (error) {
    console.error('修改用户状态失败:', error)
    // 这里可以添加用户提示
  }
}

// 组件挂载时初始化状态
onMounted(() => {
  const userInfo = getUserInfoFromStorage()
  if (userInfo?.status) {
    const statusItem = statusList.find(item => item.text === userInfo.status)
    if (statusItem) {
      currentStatus.value = {
        icon: statusItem.icon,
        text: statusItem.text,
        bgColor: statusItem.bgColor
      }
      selectedStatus.value = statusItem.text
    }
  }
})
</script>

<template>
  <div class="status-popup" :style="backgroundStyle">
    <div class="header drag">
      <div class="right-btn">
        <svg class="icon outerColor close-icon" aria-hidden="true" @click="handleClose">
          <use :xlink:href="`#icon-fork`"></use>
        </svg>
      </div>
    </div>
    <div class="current-status drag">
      <div class="icon-wrapper">
        <svg class="icon outerColor" aria-hidden="true">
          <use :xlink:href="`#${currentStatus.icon}`"></use>
        </svg>
      </div>
      <span>{{ currentStatus.text }}</span>
    </div>
    <div class="status-grid-container">
      <div class="status-grid">
        <div v-for="(status, index) in statusList" :key="index" class="status-item" :class="{
          'selected': status.text === selectedStatus,
          'hover-effect': status.text !== selectedStatus
        }" @click="handleSelect(status)">
          <svg class="icon outerColor" aria-hidden="true">
            <use :xlink:href="`#${status.icon}`"></use>
          </svg>
          <div class="text">{{ status.text }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.drag {
  -webkit-app-region: drag;
}

.status-popup {
  width: 320px;
  height: 540px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  transition: background 0.3s ease;

  .header {
    height: 30px;
    display: flex;
    justify-content: flex-end;
    align-items: flex-start;
    padding: 0;
    position: relative;

    .right-btn {
      -webkit-app-region: no-drag;
      position: absolute;
      top: 0;
      right: 0;

      .close-icon {
        font-size: 16px;
        color: #666;
        cursor: pointer;
        padding: 8px;
        transition: all 0.2s ease;
        display: block;

        &:hover {
          color: #ff4757;
          background-color: rgba(255, 71, 87, 0.1);
        }
      }
    }
  }

  .current-status {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px 0 30px;
    background: transparent;
    height: 140px;

    .icon-wrapper {
      width: 50px;
      height: 50px;
      background: #fff;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 8px;

      .icon {
        font-size: 28px;
        color: #ffd719;
      }
    }

    span {
      font-size: 14px;
      color: #333;
      font-weight: 500;
    }
  }

  .status-grid-container {
    flex: 1;
    background: #fff;
    margin: 0 10px;
    border-radius: 12px;
    overflow: hidden;
    height: 340px;
    display: flex;
    flex-direction: column;

    .status-grid {
      flex: 1;
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 8px;
      overflow-y: auto;
      scrollbar-width: none;
      -ms-overflow-style: none;
      padding: 12px;
      padding-bottom: 60px;

      &::-webkit-scrollbar {
        display: none;
      }

      .status-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        cursor: pointer;
        transition: all 0.2s;
        padding: 6px 4px;
        border-radius: 6px;

        &.selected {
          background-color: #5C9EFF; // 蓝色背景

          .text {
            color: #fff; // 文字变白
          }

          .icon {
            color: #fff; // 图标变白
          }
        }

        &.hover-effect:hover {
          background-color: #f5f5f5; // 灰色背景
        }

        .icon {
          font-size: 24px;
          margin-bottom: 4px;
        }

        .text {
          font-size: 11px;
          color: #666;
          text-align: center;
          width: 100%;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }
}
</style>
