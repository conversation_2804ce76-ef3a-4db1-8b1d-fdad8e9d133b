{"name": "copy-qq", "version": "1.0.0", "description": "An Electron application with Vue", "main": "./out/main/index.js", "author": "example.com", "homepage": "https://electron-vite.org", "scripts": {"format": "prettier --write .", "lint": "eslint . --ext .js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix", "start": "electron-vite preview", "dev": "electron-vite dev", "build": "electron-vite build", "postinstall": "electron-builder install-app-deps", "build:unpack": "npm run build && electron-builder --dir", "build:win": "npm run build && electron-builder --win", "build:mac": "npm run build && electron-builder --mac", "build:linux": "npm run build && electron-builder --linux"}, "dependencies": {"@electron-toolkit/preload": "^3.0.1", "@electron-toolkit/utils": "^3.0.0", "axios": "1.6.2", "electron-vite": "^3.1.0", "element-plus": "^2.9.3", "pinia": "^3.0.3", "vue-router": "^4.5.0"}, "devDependencies": {"@electron-toolkit/eslint-config": "^1.0.2", "@rushstack/eslint-patch": "^1.10.3", "@vitejs/plugin-vue": "^5.0.5", "@vue/eslint-config-prettier": "^9.0.0", "electron": "^31.7.7", "electron-builder": "^24.13.3", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.26.0", "prettier": "^3.3.2", "sass-embedded": "^1.83.4", "vite": "^5.3.1", "vue": "^3.4.30"}}