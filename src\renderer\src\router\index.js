import { createRouter, createWebHashHistory } from 'vue-router'
import contact from '../views/contact.vue'
import friendNotice from '../views/contact/friendNotice.vue'
import unionNotice from '../views/contact/unionNotice.vue'
import Status from '../components/status.vue'

const router = createRouter({
  history: createWebHashHistory(),
  routes: [
    {
      path: '/',
      component: () => import('../views/login.vue')
    },
    {
      path: '/home',
      redirect: '/home/<USER>',
      component: () => import('../views/home.vue'),
      children: [
        {
          path: 'message',
          component: () => import('../views/message.vue'),
          children: [
            {
              path: 'chat/:id',
              name: 'chat',
              component: () => import('../views/message/chat.vue')
            },
            {
              path: 'privateChat/:id',
              name: 'privateChat',
              component: () => import('../views/message/privateChat.vue')
            }
          ]
        },
        {
          path: 'contact',
          name: 'contact',
          component: contact,
          children: [
            {
              path: 'friend-notice',
              name: 'friendNotice',
              component: friendNotice
            },
            {
              path: 'union-notice',
              name: 'unionNotice',
              component: unionNotice
            }
          ]
        },
        {
          path: 'shoucang',
          name: 'shoucang',
          component: () => import('../views/shoucang.vue')
        }
      ]
    },
    {
      path: '/status',
      name: 'status',
      component: Status
    },
    {
      path: '/search',
      name: 'search',
      component: () => import('../views/netsearch.vue')
    },
    {
      path: '/apply',
      name: 'apply',
      component: () => import('../views/apply.vue')
    },
    {      path: '/joinGroup',      name: 'joinGroup',      component: () => import('../views/joinGroup.vue')    },
    {
      path: '/editProfile',
      name: 'editProfile',
      component: () => import('../components/user/editProfile.vue')
    }
  ]
})

export default router
