<script setup>
import { ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import chatHeader from '../../components/chatHeader.vue'
import messageInputArea from '../../components/messageInputArea.vue'
import messageListVue from '../../components/messageList.vue'
import { useChatStore } from '../../store/chatStore'
import { getChats } from '../../api/message'
import { getSendMessageInfo } from '../../utils/global'

const chatStore = useChatStore()
const route = useRoute()

// 将 chatSessionId 改为响应式
const chatSessionId = ref(Number(route.params.id))
const userInfo = ref(chatStore.getUserInfo())

// 聊天信息头部
const chatInfo = ref({
  name: '',
  memberCount: 0
})

// 聊天消息列表
const messageList = ref([])

// 群成员列表
const memberList = ref([])

// 群人数
const groupCount = ref(0)

// 最新的群公告
const groupAnnounce = ref({})

// 初始化聊天数据的函数
const initChatData = async (sessionId) => {
  try {
    if (chatStore.existMessages(sessionId)) {
      const data = chatStore.getMessages(sessionId)
      if (data) {
        // 检查数据结构：如果是从缓存获取的，包含 groupMessageChatVO 属性
        const chatData = data.groupMessageChatVO || data
        groupCount.value = chatData.groupCount || 0
        messageList.value = chatData.messagesVOList || []
        groupAnnounce.value = chatData.groupAnnounce || null
        memberList.value = chatData.groupMemberVOList || []
      } else {
        console.warn('缓存数据格式不正确:', data)
        // 如果缓存数据有问题，重新从服务器获取
        const { data: serverData } = await getChats(sessionId)
        console.log('重新从服务器获取数据:', serverData)

        if (serverData) {
          groupCount.value = serverData.groupCount || 0
          messageList.value = serverData.messagesVOList || []
          groupAnnounce.value = serverData.groupAnnounce || null
          memberList.value = serverData.groupMemberVOList || []
          // 重新持久化
          chatStore.setMessagesVO(sessionId, serverData)
        }
      }
    } else {
      console.log('从服务器获取数据')
      const { data } = await getChats(sessionId)
      console.log('服务器数据:', data)

      if (data) {
        groupCount.value = data.groupCount || 0
        messageList.value = data.messagesVOList || []
        groupAnnounce.value = data.groupAnnounce || null
        memberList.value = data.groupMemberVOList || []
        // 持久化
        chatStore.setMessagesVO(sessionId, data)
      }
    }
  } catch (error) {
    console.error('初始化聊天数据时出错:', error)
    // 设置默认值
    groupCount.value = 0
    messageList.value = []
    groupAnnounce.value = null
    memberList.value = []
  }

  const res = chatStore.getChatSession(sessionId)
  if (res) {
    chatInfo.value = {
      name: res.messageName || '',
      memberCount: groupCount.value || 0
    }
  } else {
    console.warn('未找到对应的聊天会话:', sessionId)
    chatInfo.value = {
      name: '未知群聊',
      memberCount: groupCount.value || 0
    }
  }
}

// 监听路由参数变化
watch(
  () => route.params.id,
  (newId) => {
    if (newId) {
      chatSessionId.value = Number(newId)
      chatStore.setActiveChatSessionId(chatSessionId.value)
      initChatData(chatSessionId.value)
    }
  },
  { immediate: true }
)

// 处理发送消息
const handleSendMessage = (message) => {
  const data = getSendMessageInfo(chatSessionId.value)
  data.message = message
  try {
    chatStore.sendMessage(JSON.stringify(data))
    messageList.value.push({
      messageId: Date.now() + Math.random(), // 生成临时的唯一ID
      userId: userInfo.value.userId,
      nickname: userInfo.value.nickname,
      avatar: userInfo.value.avatar,
      content: message.content,
      createTime: new Date()
    })
    // 更改此会话信息
    chatStore.updateSendMsgChatSession(message)
  } catch (e) {
    // todo 发送失败处理，消息列表增加，但是需要加红色感叹号
  }
}
</script>

<template>
  <div class="chat-container">
    <div class="drag"></div>
    <!-- 聊天头部 -->
    <chatHeader v-model="chatInfo" />

    <!-- 主要内容区域 -->
    <div class="chat-main">
      <!-- 聊天消息区域 -->
      <div class="chat-content">
        <messageListVue v-model="messageList" />
        <messageInputArea @send-message="handleSendMessage" />
      </div>

      <!-- 右侧边栏 -->
      <div class="chat-sidebar">
        <!-- 群公告 -->
        <div v-if="groupAnnounce && groupAnnounce.announceContent" class="announcement-section">
          <div class="section-header">
            <span>群公告</span>
            <svg class="icon" aria-hidden="true">
              <use xlink:href="#icon-youjiantou"></use>
            </svg>
          </div>
          <div class="announcement-content">
            {{ groupAnnounce.announceContent }}
          </div>
        </div>

        <!-- 群成员 -->
        <div class="members-section">
          <div class="section-header">
            <span>群聊成员 {{ chatInfo?.memberCount || 0 }}</span>
            <button class="search-btn">
              <svg class="icon" aria-hidden="true">
                <use xlink:href="#icon-sousuo"></use>
              </svg>
            </button>
          </div>
          <div class="members-list">
            <div v-for="member in memberList" :key="member.userId" class="member-item">
              <img :src="member.avatar" class="member-avatar" />
              <div class="member-info">
                <div class="member-name">{{ member.nickname }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.drag {
  width: 580px;
  height: 24px;
  background-color: transparent;
  -webkit-app-region: drag;
  position: absolute;
  top: 0;
  left: 50%; /* 居中定位 */
  transform: translateX(-50%); /* 居中对齐 */
  z-index: 100;
}

.chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f2f2f2;
  position: relative;
  box-sizing: border-box;

  :deep(#chatHeader .chat-header) {
    padding-top: 28px !important;
    padding-bottom: 4px !important;
  }
}

.chat-main {
  display: flex;
  flex: 1;
  overflow: hidden;
  height: 0; /* 关键：强制 flex 子元素计算高度 */
}

.chat-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #f2f2f2;

  // 消息列表区域占用剩余空间
  :deep(#messageList) {
    flex: 1;
    overflow: hidden;
    /* 防止内容溢出 */
  }

  // 消息输入区域样式 - 让组件自己控制样式
  :deep(.message-input-area) {
    flex: none; // 不参与flex伸缩
    /* 让组件自己决定其他样式 */
  }
}

.chat-sidebar {
  width: 200px;
  background-color: #f2f2f2;
  border-left: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;

  .announcement-section {
    padding: 16px;
    border-bottom: 1px solid #e0e0e0;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      span {
        font-size: 14px;
        font-weight: 500;
        color: #333;
      }

      .close-btn {
        width: 20px;
        height: 20px;
        border: none;
        background: transparent;
        cursor: pointer;
        color: #999;
        font-size: 16px;

        &:hover {
          color: #666;
        }
      }

      .icon {
        color: #999;
        font-size: 12px;
        cursor: pointer;

        &:hover {
          color: #666;
        }
      }
    }

    .announcement-content {
      font-size: 12px;
      color: #666;
      line-height: 1.4;
      max-height: 100px;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 6;
      -webkit-box-orient: vertical;
      word-break: break-word;
      margin-bottom: 10px;

      a {
        color: #1890ff;
        text-decoration: none;

        &:hover {
          text-decoration: underline;
        }
      }
    }
  }

  .members-section {
    flex: 1;
    padding: 16px;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      span {
        font-size: 14px;
        font-weight: 500;
        color: #333;
      }

      .search-btn {
        width: 20px;
        height: 20px;
        border: none;
        background: transparent;
        cursor: pointer;
        color: #999;
        font-size: 14px;

        &:hover {
          color: #666;
        }
      }
    }

    .members-list {
      max-height: 400px;
      overflow-y: auto;
      overflow-x: hidden; /* 禁用水平滚动条 */

      /* 默认隐藏滚动条 */
      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background: transparent;
        border-radius: 3px;
      }

      /* 悬停时显示滚动条 */
      &:hover {
        &::-webkit-scrollbar-thumb {
          background: #d9d9d9;

          &:hover {
            background: #bfbfbf;
          }

          &:active {
            background: #999;
          }
        }
      }

      .member-item {
        display: flex;
        align-items: center;
        padding: 8px 0;
        cursor: pointer;

        &:hover {
          background-color: #f0f0f0;
          border-radius: 4px;
          margin: 0 -8px;
          padding: 8px;
        }

        .member-avatar {
          width: 32px;
          height: 32px;
          border-radius: 4px;
          margin-right: 8px;
          flex-shrink: 0;
        }

        .member-info {
          flex: 1;
          min-width: 0;

          .member-name {
            font-size: 13px;
            color: #333;
            margin-bottom: 2px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }
  }
}
</style>
